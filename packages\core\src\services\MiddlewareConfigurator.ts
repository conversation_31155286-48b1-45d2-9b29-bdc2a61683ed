import { Context } from "koishi";

import { ChatModelSwitcher } from "../adapters";
import { Config } from "../config";
import { MiddlewareManager } from "../middlewares/base";
import { CheckReplyCondition } from "../middlewares/impl/CheckReplyCondition";
import { DatabaseStorageMiddleware } from "../middlewares/impl/DatabaseStorage";
import { ErrorHandlingMiddleware } from "../middlewares/impl/ErrorHandling";
import { LLMProcessingMiddleware } from "../middlewares/impl/LLMProcessing";
import { ResponseHandlingMiddleware } from "../middlewares/impl/ResponseHandling";
import { PromptBuilder } from "../prompts/PromptBuilder";
import { ImageProcessor } from "../shared";
import { IServiceContainer, SERVICE_TOKENS } from "./ServiceContainer";
import { DataManager } from "./worldstate/DataManager";

/**
 * 中间件配置器
 * 负责配置和组装中间件链
 */
export class MiddlewareConfigurator {
    constructor(private ctx: Context, private config: Config, private container: IServiceContainer) {}

    /**
     * 配置中间件链
     */
    public configure(): MiddlewareManager {
        const middlewareManager = this.container.get<MiddlewareManager>(SERVICE_TOKENS.MIDDLEWARE_MANAGER);

        this.setupMiddlewareChain(middlewareManager);
        this.registerCleanupHandlers();

        return middlewareManager;
    }

    private setupMiddlewareChain(middlewareManager: MiddlewareManager): void {
        // 错误处理中间件
        middlewareManager.use(
            new ErrorHandlingMiddleware(this.ctx, {
                debug: this.config.Debug.EnableDebug,
                uploadDump: this.config.Debug.UploadDump,
                pasteServiceUrl: "https://dump.yesimbot.chat/",
                includeFullSessionContent: false,
            })
        );

        // 数据库存储中间件
        const imageProcessor = this.container.get<ImageProcessor>(SERVICE_TOKENS.IMAGE_PROCESSOR);
        const dataManager = this.container.get<DataManager>(SERVICE_TOKENS.DATA_MANAGER);

        middlewareManager.use(
            new DatabaseStorageMiddleware(this.ctx, {
                imageProcessor,
                dataManager,
            })
        );

        // 检查回复条件中间件（重构版本）
        const checkReplyMiddleware = new CheckReplyCondition(this.ctx, this.config.ReplyCondition);
        middlewareManager.use(checkReplyMiddleware);

        // LLM处理中间件
        const chatModelSwitcher = this.container.get<ChatModelSwitcher>(SERVICE_TOKENS.CHAT_MODEL_SWITCHER);
        const promptBuilder = this.container.get<PromptBuilder>(SERVICE_TOKENS.PROMPT_BUILDER);

        middlewareManager.use(
            new LLMProcessingMiddleware(
                this.ctx,
                {
                    chatModelSwitcher,
                    promptBuilder,
                },
                {
                    debug: this.config.Debug.EnableDebug,
                    retryConfig: {
                        maxRetries: this.config.LLM.RetryConfig.MaxRetries,
                        timeoutMs: this.config.LLM.RetryConfig.TimeoutMs,
                        retryDelayMs: this.config.LLM.RetryConfig.RetryDelayMs,
                        exponentialBackoff: this.config.LLM.RetryConfig.ExponentialBackoff,
                        retryableErrors: this.config.LLM.RetryConfig.RetryableErrors,
                    },
                    adapterSwitchingConfig: {
                        enabled: this.config.LLM.AdapterSwitching.Enabled,
                        maxAttempts: this.config.LLM.AdapterSwitching.MaxAttempts,
                    },
                }
            )
        );

        // 响应处理中间件
        middlewareManager.use(
            new ResponseHandlingMiddleware(
                this.ctx,
                { middlewareManager },
                {
                    maxRetry: this.config.ToolCall.MaxRetry,
                    life: this.config.ToolCall.Life,
                    maxHeartbeat: this.config.Chat.MaxHeartbeat,
                }
            )
        );
    }

    private registerCleanupHandlers(): void {
        this.ctx.on("dispose", () => {
            const middlewareManager = this.container.get<MiddlewareManager>(SERVICE_TOKENS.MIDDLEWARE_MANAGER);
            const checkReply = middlewareManager.getMiddleware<CheckReplyCondition>("check-reply-condition");
            if (checkReply) {
                checkReply.destroy();
            }
        });
    }
}