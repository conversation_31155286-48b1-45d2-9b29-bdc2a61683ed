import fs from "fs";
import { mkdir, writeFile } from "fs/promises";
import { Context, Logger } from "koishi";
import path from "path";

import { ChatModel } from "../../adapters/impl/ChatModel";
import { MemoryError } from "../../shared/errors/memory.error";
import { isEmpty } from "../../shared/utils/string";
import { MemoryBlockCompressionState, MemoryServiceConfig } from "../../types/memory";
import { MemoryBlock } from "../MemoryBlock";

export const defaultCompressionPrompt = `记忆压缩汇总的基本原则与要求

1. 人物核心特征优先
   - 保留每个人的身份、特长、性格、重要习惯，去掉重复或琐碎的行为记录（如某次聊天内容）。
   - 示例：
      - ✅ 保留 "小软酱是化学博士，开发AnyChem，数学物理全能，性格傲娇"
      - ❌ 删除 "今天和小软酱玩化学游戏，她纠正了我"（非核心特征）
2. 合并同类信息
   - 同一人物的多个属性尽量合并为一句，避免分散。
   - 示例：
      - 原句：
         - "马克柴喜欢语文、数学、英语和计算机"
         - "马克柴喜欢二次元文化，玩过东方Project"
      - 合并为：
         - "马克柴擅长文科理科，爱好二次元（东方Project）"
3. 时间敏感性信息简化
   - 具体日期/事件（如考试时间）若无长期意义，可模糊化或删除。
   - 示例：
      - ❌ "2025年5月28日数学周测" → ✅ "马克柴近期有数学考试焦虑"
4. 群体行为与互动精简
   - 群聊中的临时互动（如"今天群里讨论XX"）若无特殊意义，直接删除。
   - 保留长期关系（如"群小草是赞助商"）或标志性事件（如"茴香豆称我为骗子"）。
5. 避免主观评价
   - 删除纯情绪表达（如"很讨厌""让我生气"），除非反映人物性格（如"AAA气泡鱼在意被遗忘"）。
6. 标准化表述
   - 统一称呼（如全用"马克柴"或全用"mkc"），避免混用。
   - 用简洁句式（如"人物A是XX，擅长YY，性格ZZ"）。

压缩后依然保持每行一条记忆，每一条记忆是一个完整的句子。`;

export class CompressionService {
    private _compressionStates: Map<string, MemoryBlockCompressionState> = new Map();
    private _intervalCompressionTimer: NodeJS.Timeout | null = null;
    private readonly logger: Logger;

    constructor(
        private readonly ctx: Context,
        private readonly config: MemoryServiceConfig["Compression"],
        private readonly backupConfig: MemoryServiceConfig["Backup"],
        private readonly chatModel: ChatModel
    ) {
        this.logger = ctx.logger(CompressionService.name);
    }

    public initializeCompressionStates(labels: string[]): void {
        for (const label of labels) {
            this._compressionStates.set(label, {
                MessageCount: 0,
                LastCompressionTime: new Date(),
            });
            this.logger.debug(`Initialized compression state for block "${label}".`);
        }
    }

    public startIntervalCompression(compressibleBlocks: string[]): void {
        const intervalMinutes = this.config?.IntervalMinutes ?? 0;
        if (intervalMinutes > 0) {
            this._intervalCompressionTimer = setInterval(async () => {
                this.logger.debug(`[Compression] Running scheduled check for all compressible blocks.`);
                await this._triggerTimedCompression(compressibleBlocks);
            }, intervalMinutes * 60 * 1000);
            this.logger.info(`[Compression] Scheduled compression check every ${intervalMinutes} minutes.`);
        }
    }

    public async checkAndTriggerCompression(label: string, block: MemoryBlock): Promise<void> {
        const compressionConfig = this.config;
        const state = this._compressionStates.get(label);

        if (!compressionConfig?.CompressibleBlocks?.includes(label) || !block || !state) {
            return;
        }

        const triggerWhen = compressionConfig.CompressionWhen;
        let shouldCompress = false;

        this.logger.debug(
            `[Compression] Checking ${label} for compression. Current: Lines=${block.content.length}, Chars=${block.currentSize}, Msgs=${state.MessageCount}`
        );

        switch (triggerWhen) {
            case "Lines":
                if (compressionConfig.Lines > 0 && block.content.length >= compressionConfig.Lines) {
                    shouldCompress = true;
                    this.logger.info(
                        `[Compression] ${label} triggered by Lines threshold (${block.content.length}/${compressionConfig.Lines}).`
                    );
                }
                break;
            case "Characters":
                if (compressionConfig.Characters > 0 && block.currentSize >= compressionConfig.Characters) {
                    shouldCompress = true;
                    this.logger.info(
                        `[Compression] ${label} triggered by Characters threshold (${block.currentSize}/${compressionConfig.Characters}).`
                    );
                }
                break;
            case "IntervalMessages":
                if (compressionConfig.IntervalMessages > 0 && state.MessageCount >= compressionConfig.IntervalMessages) {
                    shouldCompress = true;
                    this.logger.info(
                        `[Compression] ${label} triggered by IntervalMessages threshold (${state.MessageCount}/${compressionConfig.IntervalMessages}).`
                    );
                }
                break;
            default:
                break;
        }

        if (shouldCompress) {
            await this.performCompression(label, block, state);
        }
    }

    private async _triggerTimedCompression(compressibleBlocks: string[]): Promise<void> {
        const compressionConfig = this.config;
        if (!compressionConfig || !compressionConfig.IntervalMinutes || compressionConfig.IntervalMinutes <= 0) {
            return;
        }

        for (const label of compressibleBlocks) {
            // 这里需要从 CoreMemoryService 获取 block
            // 为了避免循环依赖，CompressionService 不直接持有 CoreMemoryService 实例
            // 而是通过参数传递或者在 MemoryManager 中协调
            // 暂时留空，待 MemoryManager 协调
            // const block = this.coreMemoryService.getBlock(label);
            // if (!block) continue;
            // const state = this._compressionStates.get(label);
            // if (!state) continue;
            // const timeDiffMinutes = (new Date().getTime() - state.lastCompressionTime.getTime()) / (1000 * 60);
            // if (compressionConfig.CompressionWhen === "IntervalMinutes" && timeDiffMinutes >= compressionConfig.IntervalMinutes) {
            //     this.logger.info(
            //         `[Compression] ${label} triggered by IntervalMinutes threshold (${timeDiffMinutes.toFixed(1)}/${
            //             compressionConfig.IntervalMinutes
            //         } mins).`
            //     );
            //     await this.performCompression(label, block, state);
            // }
        }
    }

    public async performCompression(label: string, block: MemoryBlock, state: MemoryBlockCompressionState): Promise<void> {
        const compressionConfig = this.config;
        const backupConfig = this.backupConfig;

        if (!compressionConfig || !this.chatModel || !backupConfig) {
            this.logger.error(`[Compression] Missing configuration for compression for block ${label}. Skipping compression.`);
            return;
        }

        try {
            // 1. Backup logic
            if (backupConfig.Enabled) {
                const backupDir = path.resolve(backupConfig.BackupPath);
                const timestamp = new Date().toISOString().replace(/[:.]/g, "-").replace(/[TZ]/g, "_").substring(0, 19);
                const backupFileName = `${label}_${timestamp}.txt`;
                const backupFilePath = path.join(backupDir, backupFileName);

                try {
                    if (!fs.existsSync(backupDir)) {
                        await mkdir(backupDir, { recursive: true });
                    }
                    await writeFile(backupFilePath, block.content.join("\n"), "utf-8");
                    this.logger.info(`Backed up ${label} to ${backupFilePath} before compression.`);
                } catch (error) {
                    this.logger.error(`Failed to backup ${label} to ${backupFilePath}: ${error.message}`);
                }
            }

            // 2. Prepare prompt for LLM
            const memoriesContent = block.content.join("\n");
            const compressionPrompt = compressionConfig.CustomPrompt || defaultCompressionPrompt;
            const fullPrompt = `${compressionPrompt}\n\nMemories:\n${memoriesContent}`;

            // 3. Call LLM
            this.logger.info(`Sending ${memoriesContent.length} chars to LLM for compression of ${label}...`);
            const { text } = await this.chatModel.chat([{ role: "user", content: fullPrompt }]);
            const compressedContent = text.trim();

            if (isEmpty(compressedContent)) {
                this.logger.warn(`LLM returned empty summary for ${label}. Keeping original content.`);
                return;
            }

            // 4. Update _content and persist (通过 CoreMemoryService 更新)
            await block.replaceContent([compressedContent]); // MemoryBlock 需要一个 replaceContent 方法
            state.MessageCount = 0;
            state.LastCompressionTime = new Date();
            this.logger.info(
                `[Compression] Successfully compressed and reset state for block ${label}. New content: ${block.content.length} lines, ${block.currentSize} chars.`
            );
        } catch (error) {
            this.logger.error(`[Compression] Failed to compress block ${label}: ${error.message}`);
            throw new MemoryError(`Compression failed for ${label}`, { error: error.message, label });
        }
    }

    public stop(): void {
        if (this._intervalCompressionTimer) {
            clearInterval(this._intervalCompressionTimer);
            this._intervalCompressionTimer = null;
            this.logger.info("[Compression] Stopped interval compression timer.");
        }
        this._compressionStates.clear();
        this.logger.info("CompressionService stopped.");
    }

    public incrementMessageCount(label: string): void {
        const state = this._compressionStates.get(label);
        if (state) {
            state.MessageCount++;
        }
    }
}
