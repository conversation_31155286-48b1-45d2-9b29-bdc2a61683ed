import { Context } from "koishi";
import { IMiddleware, MiddlewareContext, MiddlewarePhase, MiddlewarePriority, MiddlewareInfo } from "./MiddlewareCore";

/**
 * 抽象中间件基类
 * 提供通用的中间件实现基础
 */
export abstract class BaseMiddleware<TConfig = any> implements IMiddleware<TConfig> {
    public readonly id: string;
    public readonly name: string;
    public readonly phase: MiddlewarePhase;
    public readonly priority: MiddlewarePriority;
    public readonly enabled: boolean;
    public readonly dependencies: string[];
    public readonly config: TConfig;

    protected readonly ctx: Context;
    protected readonly logger: any;

    constructor(
        id: string,
        name: string,
        phase: MiddlewarePhase,
        ctx: Context,
        config: TConfig,
        options: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        } = {}
    ) {
        this.id = id;
        this.name = name;
        this.phase = phase;
        this.ctx = ctx;
        this.config = config;
        this.priority = options.priority ?? MiddlewarePriority.NORMAL;
        this.enabled = options.enabled ?? true;
        this.dependencies = options.dependencies ?? [];
        this.logger = ctx.logger(`Middleware:${name}`);
    }

    /**
     * 执行中间件逻辑 - 子类必须实现
     */
    abstract execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void>;

    /**
     * 初始化中间件 - 子类可选实现
     */
    async initialize?(): Promise<void>;

    /**
     * 清理资源 - 子类可选实现
     */
    async dispose?(): Promise<void>;

    /**
     * 健康检查 - 子类可选实现
     */
    async healthCheck?(): Promise<boolean>;

    /**
     * 获取中间件信息
     */
    getInfo(): MiddlewareInfo {
        return {
            id: this.id,
            name: this.name,
            phase: this.phase,
            priority: this.priority,
            enabled: this.enabled,
            dependencies: this.dependencies,
        };
    }

    /**
     * 记录性能指标
     */
    protected recordMetric(ctx: MiddlewareContext, key: string, value: number): void {
        ctx.metadata.performance.set(`${this.id}.${key}`, value);
    }

    /**
     * 获取共享数据
     */
    protected getShared<T>(ctx: MiddlewareContext, key: string): T | undefined {
        return ctx.shared.get(key);
    }

    /**
     * 设置共享数据
     */
    protected setShared<T>(ctx: MiddlewareContext, key: string, value: T): void {
        ctx.shared.set(key, value);
    }

    /**
     * 检查依赖是否已执行
     */
    protected checkDependencies(ctx: MiddlewareContext): boolean {
        return this.dependencies.every((dep) => ctx.metadata.executedMiddlewares.includes(dep));
    }

    /**
     * 安全执行异步操作
     */
    protected async safeExecute<T>(operation: () => Promise<T>, errorMessage: string, defaultValue?: T): Promise<T | undefined> {
        try {
            return await operation();
        } catch (error) {
            this.logger.error(`${errorMessage}:`, error);
            return defaultValue;
        }
    }
}

/**
 * 预处理阶段中间件基类
 */
export abstract class PreprocessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        id: string,
        name: string,
        ctx: Context,
        config: TConfig,
        options?: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        }
    ) {
        super(id, name, MiddlewarePhase.PREPROCESSING, ctx, config, options);
    }
}

/**
 * 输入处理阶段中间件基类
 */
export abstract class InputProcessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        id: string,
        name: string,
        ctx: Context,
        config: TConfig,
        options?: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        }
    ) {
        super(id, name, MiddlewarePhase.INPUT_PROCESSING, ctx, config, options);
    }
}

/**
 * 条件检查阶段中间件基类
 */
export abstract class ConditionCheckMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        id: string,
        name: string,
        ctx: Context,
        config: TConfig,
        options?: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        }
    ) {
        super(id, name, MiddlewarePhase.CONDITION_CHECK, ctx, config, options);
    }
}

/**
 * 核心处理阶段中间件基类
 */
export abstract class CoreProcessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        id: string,
        name: string,
        ctx: Context,
        config: TConfig,
        options?: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        }
    ) {
        super(id, name, MiddlewarePhase.CORE_PROCESSING, ctx, config, options);
    }
}

/**
 * 输出处理阶段中间件基类
 */
export abstract class OutputProcessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        id: string,
        name: string,
        ctx: Context,
        config: TConfig,
        options?: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        }
    ) {
        super(id, name, MiddlewarePhase.OUTPUT_PROCESSING, ctx, config, options);
    }
}

/**
 * 后处理阶段中间件基类
 */
export abstract class PostprocessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        id: string,
        name: string,
        ctx: Context,
        config: TConfig,
        options?: {
            priority?: MiddlewarePriority;
            enabled?: boolean;
            dependencies?: string[];
        }
    ) {
        super(id, name, MiddlewarePhase.POSTPROCESSING, ctx, config, options);
    }
}

/**
 * 中间件装饰器 - 用于自动注册中间件
 */
export function middleware(metadata: {
    id: string;
    name: string;
    phase: MiddlewarePhase;
    priority?: MiddlewarePriority;
    dependencies?: string[];
}) {
    return function <T extends new (...args: any[]) => BaseMiddleware>(constructor: T) {
        // 在构造函数上添加元数据
        (constructor as any).middlewareMetadata = metadata;
        return constructor;
    };
}

/**
 * 配置验证装饰器
 */
export function validateConfig<TConfig>(validator: (config: TConfig) => boolean | string) {
    return function <T extends new (...args: any[]) => BaseMiddleware<TConfig>>(constructor: T) {
        (constructor as any).configValidator = validator;
        return constructor;
    };
}
