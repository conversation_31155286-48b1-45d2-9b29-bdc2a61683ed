import { Context } from "koishi";
import { MessageContext } from "./base";

/**
 * 简化的中间件基类
 * 减少构造函数参数，使用更直观的依赖注入
 */
export abstract class SimpleMiddleware {
    public readonly name: string;
    protected readonly ctx: Context;
    protected readonly logger: any;

    constructor(name: string, ctx: Context) {
        this.name = name;
        this.ctx = ctx;
        this.logger = ctx.logger(name);
    }

    /**
     * 执行中间件逻辑
     */
    abstract execute(ctx: MessageContext, next: () => Promise<void>): Promise<void>;

    /**
     * 可选的初始化方法
     */
    async initialize?(): Promise<void>;

    /**
     * 可选的清理方法
     */
    async dispose?(): Promise<void>;
}

/**
 * 中间件工厂函数类型
 */
export type MiddlewareFactory<T = any> = (ctx: Context, config: T) => SimpleMiddleware;

/**
 * 中间件配置接口
 */
export interface MiddlewareConfig {
    name: string;
    factory: MiddlewareFactory;
    config?: any;
    enabled?: boolean;
}

/**
 * 简化的中间件管理器
 */
export class SimpleMiddlewareManager {
    private middlewares: SimpleMiddleware[] = [];
    private configs: MiddlewareConfig[] = [];

    constructor(private ctx: Context) {}

    /**
     * 注册中间件配置
     */
    register(config: MiddlewareConfig): this {
        this.configs.push(config);
        return this;
    }

    /**
     * 初始化所有中间件
     */
    async initialize(): Promise<void> {
        for (const config of this.configs) {
            if (config.enabled === false) continue;
            
            const middleware = config.factory(this.ctx, config.config);
            this.middlewares.push(middleware);
            
            if (middleware.initialize) {
                await middleware.initialize();
            }
        }
    }

    /**
     * 执行中间件链
     */
    async execute(ctx: MessageContext): Promise<void> {
        await this.executeFrom(ctx, 0);
    }

    /**
     * 从指定位置开始执行中间件链
     */
    private async executeFrom(ctx: MessageContext, startIndex: number): Promise<void> {
        const dispatch = async (index: number): Promise<void> => {
            if (index >= this.middlewares.length) return;
            const middleware = this.middlewares[index];
            await middleware.execute(ctx, () => dispatch(index + 1));
        };
        await dispatch(startIndex);
    }

    /**
     * 获取指定名称的中间件
     */
    getMiddleware<T extends SimpleMiddleware>(name: string): T | undefined {
        return this.middlewares.find(m => m.name === name) as T;
    }

    /**
     * 清理所有中间件
     */
    async dispose(): Promise<void> {
        for (const middleware of this.middlewares) {
            if (middleware.dispose) {
                await middleware.dispose();
            }
        }
        this.middlewares = [];
        this.configs = [];
    }
}
