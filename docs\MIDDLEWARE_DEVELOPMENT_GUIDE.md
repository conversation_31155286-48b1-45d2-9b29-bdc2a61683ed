# 中间件开发指南

## 概述

YesImBot 2.0 采用了全新的中间件架构，提供了强大的插件化能力和清晰的开发接口。本指南将帮助您了解如何开发自定义中间件。

## 核心概念

### 中间件阶段 (MiddlewarePhase)

中间件按照以下阶段顺序执行：

1. **PREPROCESSING** - 预处理阶段（错误处理、日志记录等）
2. **INPUT_PROCESSING** - 输入处理阶段（消息解析、存储等）
3. **CONDITION_CHECK** - 条件检查阶段（回复条件判断等）
4. **CORE_PROCESSING** - 核心处理阶段（LLM调用、推理等）
5. **OUTPUT_PROCESSING** - 输出处理阶段（响应生成、工具调用等）
6. **POSTPROCESSING** - 后处理阶段（清理、统计等）

### 中间件优先级 (MiddlewarePriority)

- **HIGHEST** (1000) - 最高优先级
- **HIGH** (800) - 高优先级
- **NORMAL** (500) - 普通优先级
- **LOW** (200) - 低优先级
- **LOWEST** (100) - 最低优先级

## 开发步骤

### 1. 创建中间件类

```typescript
import { Context } from "koishi";
import { 
    BaseMiddleware, 
    middleware, 
    validateConfig 
} from "../middlewares/core/BaseMiddleware";
import { 
    MiddlewareContext, 
    MiddlewarePhase, 
    MiddlewarePriority 
} from "../middlewares/core/MiddlewareCore";

// 定义配置接口
interface MyMiddlewareConfig {
    enabled: boolean;
    threshold: number;
    options: string[];
}

// 使用装饰器定义中间件元数据
@middleware({
    id: "custom.my-middleware",
    name: "我的自定义中间件",
    phase: MiddlewarePhase.INPUT_PROCESSING,
    priority: MiddlewarePriority.NORMAL,
    dependencies: ["builtin.error-handling"] // 可选的依赖
})
@validateConfig<MyMiddlewareConfig>((config) => {
    if (config.threshold < 0) {
        return "threshold 不能为负数";
    }
    return true;
})
export class MyMiddleware extends BaseMiddleware<MyMiddlewareConfig> {
    constructor(ctx: Context, config: MyMiddlewareConfig) {
        super(
            "custom.my-middleware",
            "我的自定义中间件",
            MiddlewarePhase.INPUT_PROCESSING,
            ctx,
            config,
            { 
                priority: MiddlewarePriority.NORMAL,
                dependencies: ["builtin.error-handling"]
            }
        );
    }
    
    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 前置处理
        this.logger.debug("开始处理消息");
        
        // 检查配置
        if (!this.config.enabled) {
            await next();
            return;
        }
        
        // 业务逻辑
        const result = await this.processMessage(ctx);
        
        // 设置共享数据
        this.setShared(ctx, "myResult", result);
        
        // 继续执行下一个中间件
        await next();
        
        // 后置处理
        this.logger.debug("消息处理完成");
    }
    
    private async processMessage(ctx: MiddlewareContext): Promise<any> {
        // 实现您的业务逻辑
        return { processed: true };
    }
    
    // 可选的生命周期方法
    async initialize(): Promise<void> {
        this.logger.info("中间件初始化完成");
    }
    
    async dispose(): Promise<void> {
        this.logger.info("中间件已清理");
    }
    
    async healthCheck(): Promise<boolean> {
        return true;
    }
}
```

### 2. 使用阶段特定的基类

为了简化开发，您可以使用阶段特定的基类：

```typescript
import { 
    PreprocessingMiddleware,
    InputProcessingMiddleware,
    ConditionCheckMiddleware,
    CoreProcessingMiddleware,
    OutputProcessingMiddleware,
    PostprocessingMiddleware
} from "../middlewares/core/BaseMiddleware";

// 预处理中间件
export class MyPreprocessingMiddleware extends PreprocessingMiddleware<MyConfig> {
    constructor(ctx: Context, config: MyConfig) {
        super("my-preprocessing", "预处理中间件", ctx, config);
    }
    
    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 预处理逻辑
        await next();
    }
}

// 条件检查中间件
export class MyConditionMiddleware extends ConditionCheckMiddleware<MyConfig> {
    constructor(ctx: Context, config: MyConfig) {
        super("my-condition", "条件检查中间件", ctx, config);
    }
    
    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        if (this.shouldProcess(ctx)) {
            await next();
        } else {
            ctx.skip("不满足处理条件");
        }
    }
    
    private shouldProcess(ctx: MiddlewareContext): boolean {
        // 实现条件检查逻辑
        return true;
    }
}
```

### 3. 注册中间件

```typescript
import { MiddlewareRegistry } from "../middlewares/core/MiddlewareCore";

export function registerMyMiddleware(registry: MiddlewareRegistry): void {
    registry.register("custom.my-middleware", {
        factory: (ctx, config) => new MyMiddleware(ctx, config),
        defaultConfig: {
            enabled: true,
            threshold: 10,
            options: ["option1", "option2"]
        },
        validateConfig: (config) => {
            if (config.threshold < 0) {
                return "threshold 不能为负数";
            }
            return true;
        },
        metadata: {
            id: "custom.my-middleware",
            name: "我的自定义中间件",
            phase: MiddlewarePhase.INPUT_PROCESSING,
            priority: MiddlewarePriority.NORMAL,
            dependencies: [],
            version: "1.0.0",
            description: "这是一个示例中间件",
            author: "Your Name"
        }
    });
}
```

### 4. 在管道中使用

```typescript
import { MiddlewarePipeline } from "../middlewares/core/MiddlewareCore";

// 添加到管道
pipeline.add("custom.my-middleware", {
    enabled: true,
    threshold: 20,
    options: ["custom-option"]
});

// 构建管道
await pipeline.build();
```

## 最佳实践

### 1. 错误处理

```typescript
async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
    try {
        // 业务逻辑
        const result = await this.safeExecute(
            () => this.processMessage(ctx),
            "处理消息失败",
            { default: "value" }
        );
        
        await next();
    } catch (error) {
        this.logger.error("中间件执行失败:", error);
        // 决定是否继续执行或跳过
        throw error; // 或者 ctx.skip("处理失败");
    }
}
```

### 2. 性能监控

```typescript
async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
        // 业务逻辑
        await next();
    } finally {
        const duration = Date.now() - startTime;
        this.recordMetric(ctx, "execution_time", duration);
        
        if (duration > 1000) {
            this.logger.warn(`中间件执行耗时过长: ${duration}ms`);
        }
    }
}
```

### 3. 共享数据

```typescript
async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
    // 获取其他中间件设置的数据
    const previousResult = this.getShared<any>(ctx, "previousResult");
    
    // 处理逻辑
    const myResult = await this.process(previousResult);
    
    // 设置数据供后续中间件使用
    this.setShared(ctx, "myResult", myResult);
    
    await next();
}
```

### 4. 依赖检查

```typescript
async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
    // 检查依赖是否已执行
    if (!this.checkDependencies(ctx)) {
        this.logger.warn("依赖中间件未执行，跳过处理");
        ctx.skip("依赖未满足");
        return;
    }
    
    await next();
}
```

## 配置管理

### 类型安全的配置

```typescript
interface MyMiddlewareConfig {
    // 基础配置
    enabled: boolean;
    
    // 数值配置（带验证）
    threshold: number; // 0-100
    
    // 字符串配置
    mode: "strict" | "loose" | "disabled";
    
    // 数组配置
    allowedChannels: string[];
    
    // 对象配置
    advanced: {
        retryCount: number;
        timeout: number;
    };
}

// 配置验证
@validateConfig<MyMiddlewareConfig>((config) => {
    if (config.threshold < 0 || config.threshold > 100) {
        return "threshold 必须在 0-100 之间";
    }
    
    if (config.advanced.retryCount < 0) {
        return "retryCount 不能为负数";
    }
    
    return true;
})
```

### 默认配置

```typescript
const defaultConfig: MyMiddlewareConfig = {
    enabled: true,
    threshold: 50,
    mode: "strict",
    allowedChannels: [],
    advanced: {
        retryCount: 3,
        timeout: 5000
    }
};
```

## 测试

### 单元测试示例

```typescript
import { Context } from "koishi";
import { MyMiddleware } from "./MyMiddleware";
import { createMockContext } from "../test/utils";

describe("MyMiddleware", () => {
    let middleware: MyMiddleware;
    let mockContext: Context;
    
    beforeEach(() => {
        mockContext = createMockContext();
        middleware = new MyMiddleware(mockContext, {
            enabled: true,
            threshold: 10,
            options: []
        });
    });
    
    afterEach(async () => {
        await middleware.dispose();
    });
    
    it("should process message when enabled", async () => {
        const ctx = createMockMiddlewareContext();
        let nextCalled = false;
        
        await middleware.execute(ctx, async () => {
            nextCalled = true;
        });
        
        expect(nextCalled).toBe(true);
        expect(ctx.shared.get("myResult")).toBeDefined();
    });
    
    it("should skip processing when disabled", async () => {
        middleware.config.enabled = false;
        const ctx = createMockMiddlewareContext();
        let nextCalled = false;
        
        await middleware.execute(ctx, async () => {
            nextCalled = true;
        });
        
        expect(nextCalled).toBe(true);
        expect(ctx.shared.get("myResult")).toBeUndefined();
    });
});
```

## 调试和监控

### 日志记录

```typescript
// 使用结构化日志
this.logger.info("处理开始", {
    userId: ctx.koishiSession.userId,
    channelId: ctx.koishiSession.channelId,
    messageLength: ctx.koishiSession.content.length
});

// 性能日志
this.logger.debug("处理完成", {
    duration: Date.now() - startTime,
    result: "success"
});
```

### 健康检查

```typescript
async healthCheck(): Promise<boolean> {
    try {
        // 检查依赖服务
        const service = this.ctx["required.service"];
        if (!service) return false;
        
        // 检查配置
        if (!this.config.enabled) return false;
        
        // 检查资源状态
        const memoryUsage = process.memoryUsage();
        if (memoryUsage.heapUsed > 1024 * 1024 * 1024) { // 1GB
            return false;
        }
        
        return true;
    } catch (error) {
        this.logger.error("健康检查失败:", error);
        return false;
    }
}
```

## 总结

通过遵循本指南，您可以：

1. 创建类型安全、可测试的中间件
2. 利用阶段化架构实现清晰的职责分离
3. 使用装饰器简化配置和元数据管理
4. 实现高性能、可监控的中间件
5. 构建可扩展的插件生态系统

重构后的中间件系统提供了强大的功能和灵活性，同时保持了简单易用的开发体验。
