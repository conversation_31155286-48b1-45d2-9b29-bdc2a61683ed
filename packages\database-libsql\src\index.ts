import { Context, Schema, Service, Database } from "koishi";
import { default as LibSQL } from "libsql";
import path from "path";
import { Driver, Eval, executeUpdate, Field, getCell, hasSubquery, isEvalExpr, Modifier, Selection, Update, z } from 'minato'

interface Config {
    path: string;
}

const Config: Schema<Config> = Schema.object({
    path: Schema.string().required().description("数据库路径"),
});

declare module "koishi" {
    interface Context {
        libsql: LibsqlDatabase;
    }
}

export default class LibsqlDatabase extends Database {
    static readonly Config = Config;
    static readonly inject = ["model"];
    static readonly name = "database-libsql";

    static [Service.provide] = "libsql"

    db: LibSQL.Database;

    constructor(ctx: Context, config: Config) {
        super(ctx);
        this.db = new LibSQL(`file:${path.resolve(ctx.baseDir, config.path)}`)
        ctx.model.drivers.forEach((driver) => {
            ctx.logger.info("registering driver %s", driver.ctx.name)
        })
    }

    // start(): Promise<void> {
    //     this.logger.info("libsql database started")
    //     return Promise.resolve()
    // }
    // stop(): Promise<void> {
    //     this.logger.info("libsql database stopped")
    //     return Promise.resolve()
    // }
    // drop(table: string): Promise<void> {
    //     this.logger.info("dropping table %s", table)
    //     return Promise.resolve()
    // }
    // dropAll(): Promise<void> {
    //     this.logger.info("dropping all tables")
    //     return Promise.resolve()
    // }
    // stats(): Promise<Partial<Driver.Stats>> {
    //     return Promise.resolve({
    //         size: 0,
    //         tables: {},
    //     })
    // }
    // prepare(name: string): Promise<void> {
    //     this.logger.info("preparing table %s", name)
    //     return Promise.resolve()
    // }
    // get(sel: Selection.Immutable, modifier: Modifier): Promise<any> {
    //     this.logger.info("getting data from table %s with modifier %s", sel.table, modifier)
    //     return Promise.resolve()
    // }
    // eval(sel: Selection.Immutable, expr: Eval.Expr): Promise<any> {
    //     this.logger.info("evaluating data from table %s with expr %s", sel.table, expr)
    //     return Promise.resolve()
    // }
    // set(sel: Selection.Mutable, data: Update): Promise<Driver.WriteResult> {
    //     this.logger.info("setting data to table %s", sel.table)
    //     return Promise.resolve({
    //         inserted: 0,
    //         matched: 0,
    //         modified: 0,
    //         removed: 0,
    //     })
    // }
    // remove(sel: Selection.Mutable): Promise<Driver.WriteResult> {
    //     this.logger.info("removing data from table %s", sel.table)
    //     return Promise.resolve({
    //         inserted: 0,
    //         matched: 0,
    //         modified: 0,
    //         removed: 0,
    //     })
    // }
    // create(sel: Selection.Mutable, data: any): Promise<any> {
    //     this.logger.info("creating data to table %s", sel.table)
    //     return Promise.resolve()
    // }
    // upsert(sel: Selection.Mutable, data: any[], keys: string[]): Promise<Driver.WriteResult> {
    //     this.logger.info("upserting data to table %s", sel.table)
    //     return Promise.resolve({
    //         inserted: 0,
    //         matched: 0,
    //         modified: 0,
    //         removed: 0,
    //     })
    // }
    // withTransaction(callback: (session?: any) => Promise<void>): Promise<void> {
    //     this.logger.info("starting transaction")
    //     return Promise.resolve().then(callback)
    // }
    // getIndexes(table: string): Promise<Driver.Index[]> {
    //     this.logger.info("getting indexes from table %s", table)
    //     return Promise.resolve([])
    // }
    // createIndex(table: string, index: Driver.Index): Promise<void> {
    //     this.logger.info("creating index %s to table %s", index.name, table)
    //     return Promise.resolve()
    // }
    // dropIndex(table: string, name: string): Promise<void> {
    //     this.logger.info("dropping index %s from table %s", name, table)
    //     return Promise.resolve()
    // }
}
