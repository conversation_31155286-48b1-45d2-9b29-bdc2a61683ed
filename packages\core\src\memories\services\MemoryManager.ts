import { ChatModel } from "../../adapters/impl/ChatModel";
import { MemoryError } from "../../shared/errors/memory.error";
import { MemoryServiceConfig } from "../../types/memory";
import { Context, Service } from "koishi";
import { MemoryBlock } from "../MemoryBlock";
import {
    ArchivalEntry,
    ArchivalSearchResult,
    DatabaseMemoryBlockStore,
    IArchivalMemoryStore,
    IMemoryBlockStore,
    InMemoryArchivalStore,
} from "../stores";
import { CompressionService } from "./CompressionService";
import { CoreMemoryService } from "./CoreMemoryService";

export class MemoryManager extends Service {
    static readonly inject = ["yesimbot.model"];

    private coreMemoryService: CoreMemoryService;
    private compressionService: CompressionService;
    private archivalStore: IArchivalMemoryStore;
    private memoryBlockStore: IMemoryBlockStore;

    private lastModified: Date = new Date();

    constructor(ctx: Context, public readonly config: MemoryServiceConfig = {}) {
        super(ctx, "yesimbot.memory", true);

        // 初始化存储层
        this.memoryBlockStore = new DatabaseMemoryBlockStore(ctx);
        this.archivalStore = new InMemoryArchivalStore(ctx);

        // 初始化核心服务
        this.coreMemoryService = new CoreMemoryService(ctx, this.memoryBlockStore);

        // 获取 ChatModel 用于压缩服务
        const chatModel: ChatModel = ctx["yesimbot.model"].getChatModel(config.UseModel);
        this.compressionService = new CompressionService(ctx, config.Compression, config.Backup, chatModel);

        ctx.logger.info("MemoryManager initialized.");
    }

    protected async start() {
        this.ctx.logger.info("Starting MemoryManager and initializing core blocks...");
        if (this.config.CoreBlockDefaults) {
            const compressibleLabels: string[] = [];
            for (const label in this.config.CoreBlockDefaults) {
                if (Object.prototype.hasOwnProperty.call(this.config.CoreBlockDefaults, label)) {
                    const blockConfig = this.config.CoreBlockDefaults[label] || {};
                    await this.coreMemoryService.getOrCreateBlock(label, blockConfig);
                    this.ctx.logger.info(`Core memory block "${label}" ensured.`);

                    if (this.config.Compression?.CompressibleBlocks?.includes(label)) {
                        compressibleLabels.push(label);
                    }
                }
            }
            this.compressionService.initializeCompressionStates(compressibleLabels);
            this.compressionService.startIntervalCompression(compressibleLabels);
        } else {
            this.ctx.logger.info(
                `No CoreBlockDefaults configured. Standard blocks like "persona" or "human" should be explicitly created if needed or defined in config.`
            );
        }
    }

    public getCoreMemoryBlock(label: string): MemoryBlock | undefined {
        return this.coreMemoryService.getBlock(label);
    }

    public async appendToCoreMemory(label: string, content: string): Promise<string> {
        await this.coreMemoryService.appendToBlock(label, content);
        this.lastModified = new Date();
        const block = this.coreMemoryService.getBlock(label);
        if (block) {
            this.compressionService.incrementMessageCount(label);
            await this.compressionService.checkAndTriggerCompression(label, block);
        }
        return `Successfully appended to core memory block <${label}>.`;
    }

    public async replaceInCoreMemory(label: string, oldContent: string, newContent: string): Promise<string> {
        await this.coreMemoryService.replaceInBlock(label, oldContent, newContent);
        this.lastModified = new Date();
        return `Successfully replaced content in core memory block <${label}>.`;
    }

    public async storeInArchivalMemory(content: string, metadata?: Record<string, any>): Promise<ArchivalEntry> {
        try {
            const entry = await this.archivalStore.store(content, metadata);
            this.lastModified = new Date();
            this.ctx.logger.info(`Stored in archival memory. ID: ${entry.id}`);
            return entry;
        } catch (error) {
            this.ctx.logger.error(`Failed to store in archival memory: ${error.message}`);
            throw new MemoryError(`Store in archival memory failed`, { content, metadata, error });
        }
    }

    public async searchArchivalMemory(
        query: string,
        options?: { page?: number; pageSize?: number; filterMetadata?: Record<string, any> }
    ): Promise<ArchivalSearchResult> {
        try {
            const searchResult = await this.archivalStore.search(query, options);
            this.ctx.logger.info(
                `Archival search for "${query}" returned ${searchResult.results.length} of ${searchResult.total} results.`
            );
            return searchResult;
        } catch (error) {
            this.ctx.logger.error(`Failed to search archival memory: ${error.message}`);
            throw new MemoryError(`Search archival memory failed`, { query, options, error });
        }
    }

    public async getProvider(): Promise<{
        lastModified: string;
        archivalCount: number;
        memoryBlocks: MemoryBlock[];
    }> {
        return {
            lastModified: this.lastModified.toISOString(),
            archivalCount: await this.archivalStore.count(),
            memoryBlocks: this.coreMemoryService.getAllBlocks(),
        };
    }

    public getArchivalStore() {
        return this.archivalStore;
    }

    public async compression(label: string): Promise<void> {
        const block = this.coreMemoryService.getBlock(label);
        if (!block) {
            throw new MemoryError(`Memory block "${label}" not found for compression.`, { label });
        }
        // 直接调用 CompressionService 的 performCompression
        await this.compressionService.performCompression(label, block, this.compressionService["_compressionStates"].get(label)!);
    }

    protected async stop() {
        this.ctx.logger.info("Stopping MemoryManager...");
        this.compressionService.stop();
        await this.coreMemoryService.dispose();
        if (this.archivalStore.clearAll) {
            await this.archivalStore.clearAll().catch((e) => this.ctx.logger.warn(`Error clearing archival store: ${e.message}`));
        }
        this.ctx.logger.info("MemoryManager stopped and resources cleaned up.");
    }
}
