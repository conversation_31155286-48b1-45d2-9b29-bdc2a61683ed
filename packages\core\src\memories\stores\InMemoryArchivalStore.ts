import { Context } from "koishi";

export interface ArchivalEntry {
    id: string;
    content: string;
    metadata?: Record<string, any>;
    timestamp: Date;
}

export interface ArchivalSearchResult {
    results: ArchivalEntry[];
    total: number;
    page: number;
    pageSize: number;
}

export interface IArchivalMemoryStore {
    store(content: string, metadata?: Record<string, any>): Promise<ArchivalEntry>;
    search(
        query: string,
        options?: { page?: number; pageSize?: number; filterMetadata?: Record<string, any> }
    ): Promise<ArchivalSearchResult>;
    count(): Promise<number>;
    clearAll?(): Promise<void>;
}

export class InMemoryArchivalStore implements IArchivalMemoryStore {
    private _store: Map<string, ArchivalEntry> = new Map(); // 重命名为 _store
    private readonly logger: any;

    constructor(private readonly ctx: Context) {
        this.logger = ctx.logger(InMemoryArchivalStore.name);
    }

    async store(content: string, metadata?: Record<string, any>): Promise<ArchivalEntry> {
        const id = `archival-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const entry: ArchivalEntry = {
            id,
            content,
            metadata,
            timestamp: new Date(),
        };
        this._store.set(id, entry); // 使用 _store
        this.logger.debug(`Stored archival entry: ${id}`);
        return entry;
    }

    async search(
        query: string,
        options?: { page?: number; pageSize?: number; filterMetadata?: Record<string, any> }
    ): Promise<ArchivalSearchResult> {
        const allResults = Array.from(this._store.values()).filter((entry) => {
            // Simple text search for demonstration
            const matchesContent = entry.content.includes(query);
            // Add more sophisticated filtering based on metadata if needed
            return matchesContent;
        });

        const page = options?.page ?? 1;
        const pageSize = options?.pageSize ?? 10;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        const paginatedResults = allResults.slice(startIndex, endIndex);

        this.logger.debug(
            `Searched archival memory for "${query}", found ${allResults.length} results, returning page ${page} of ${pageSize}.`
        );

        return {
            results: paginatedResults,
            total: allResults.length,
            page,
            pageSize,
        };
    }

    async count(): Promise<number> {
        return this._store.size;
    }

    async clearAll(): Promise<void> {
        this._store.clear();
        this.logger.info("Cleared all archival memory entries.");
    }
}
