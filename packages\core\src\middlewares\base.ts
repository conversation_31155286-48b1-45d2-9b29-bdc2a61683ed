import { Context, Session } from "koishi";
import type { GenerateTextResult } from "xsai";
import { DefaultPlatform, OneBotPlatform, PlatformAdapter } from "../services/PlatformAdapter";
import type { AgentResponse } from "../services/worldstate/interfaces";

/**
 * 会话状态枚举
 * 简化为三个核心状态
 */
export enum ConversationState {
    IDLE, // 空闲状态，等待新消息触发
    PROCESSING, // 处理中状态
    RESPONDING, // 响应中状态
}

/**
 * 消息上下文
 * 在中间件链中传递的上下文对象
 */
export class MessageContext {
    // 当前会话状态
    public state: ConversationState = ConversationState.IDLE;

    // LLM响应和处理后的响应
    public llmResponse?: GenerateTextResult;
    public processedResponse?: string[];

    public isMentioned: boolean = false;

    // heartbeat触发次数计数器
    public heartbeatCount: number = 0;

    public currentTurnId: string;
    public agentResponses: AgentResponse[] = [];

    public platform: PlatformAdapter;

    private constructor(
        // Koishi上下文对象
        public koishiContext: Context,
        // Koishi会话对象
        public koishiSession: Session,

        public allowedChannels: string[]
    ) {
        this.isMentioned = koishiSession.stripped.atSelf;
        let platformAdapter: PlatformAdapter;
        if (koishiSession.platform === "onebot") {
            platformAdapter = new OneBotPlatform(koishiSession);
        } else {
            platformAdapter = new DefaultPlatform(koishiSession);
        }
        this.platform = platformAdapter;
    }

    // 提供一个公共的、异步的创建方法
    public static async create(koishiContext: Context, koishiSession: Session, allowedChannels: string[]): Promise<MessageContext> {
        const messageContext = new MessageContext(koishiContext, koishiSession, allowedChannels);
        await messageContext.initializeTurn(); // 等待异步操作完成
        return messageContext;
    }

    private async initializeTurn(): Promise<void> {
        const turn = await this.koishiContext["yesimbot.data"].getLastTurn(this.koishiSession.platform, this.koishiSession.channelId);
        if (turn) {
            this.currentTurnId = turn.id;
        } else {
            const newTurn = await this.koishiContext["yesimbot.data"].startNewTurn(
                this.koishiSession.platform,
                this.koishiSession.channelId
            );
            this.currentTurnId = newTurn.id;
            this.koishiContext.logger.info(`[Turn] Started new turn: ${this.currentTurnId}`);
        }
    }

    /**
     * 转换会话状态
     */
    async transitionTo(newState: ConversationState): Promise<void> {
        this.state = newState;
    }
}

/**
 * 中间件接口
 */
export abstract class Middleware {
    public readonly name: string; // 中间件名称
    protected readonly ctx: Context; // Koishi上下文对象
    protected readonly services: any; // 服务对象
    protected readonly config: any; // 配置对象

    constructor(name: string, ctx: Context, services?: any, config?: any) {
        this.name = name;
        this.ctx = ctx;
        this.services = services;
        this.config = config;
    }

    // 执行中间件
    abstract execute(ctx: MessageContext, next: () => Promise<void>): Promise<void>;
}

/**
 * 中间件管理器
 * 负责注册和执行中间件链
 */
export class MiddlewareManager {
    // 中间件列表
    public middlewares: Middleware[] = [];

    /**
     * 注册中间件
     */
    use(middleware: Middleware): this {
        this.middlewares.push(middleware);
        return this;
    }

    /**
     * 执行中间件链
     */
    async execute(ctx: MessageContext): Promise<void> {
        await this.executeFrom(ctx, 0);
    }

    /**
     * 从指定位置开始执行中间件链
     */
    async executeFrom(ctx: MessageContext, startIndex: number): Promise<void> {
        const dispatch = async (index: number): Promise<void> => {
            if (index >= this.middlewares.length) return;
            const middleware = this.middlewares[index];
            await middleware.execute(ctx, () => dispatch(index + 1));
        };
        await dispatch(startIndex);
    }

    /**
     * 获取指定名称的中间件
     */
    public getMiddleware<T extends Middleware>(name: string): T | undefined {
        return this.middlewares.find((m) => m.name === name) as T;
    }

    public findIndex(name: string): number {
        return this.middlewares.findIndex((m) => m.name === name);
    }
}
