import { createOpenAI } from "@xsai-ext/providers-cloud";
import { streamObject } from "@xsai/stream-object";
import { z } from "zod";

const { partialObjectStream } = await streamObject({
    ...createOpenAI(process.env.API_KEY_GOD, "https://api.gptgod.online/v1").chat("deepseek-v3-250324"),
    messages: [
        {
            content: `Extract the event information. Output in JSON format directly. NO CODEBLOCK REQUIRED! 重要！JSON对象外面***不需要***使用\`\`\`json...\`\`\`包围！`,
            role: "user",
        },
        {
            content: "<PERSON> and <PERSON> are going to a science fair on Friday.",
            role: "user",
        },
    ],
    schema: z.object({
        name: z.string(),
        date: z.string(),
        participants: z.array(z.string()),
    }),
});

for await (const partialObject of partialObjectStream) {
    console.log(partialObject);
}

// const { elementStream, textStream } = await streamObject({
//     ...createOpenAI(process.env.API_KEY_GOD, "https://api.gptgod.online/v1").chat("deepseek-v3-250324"),
//     messages: [
//         {
//             content: `Generate 3 hero descriptions for a fantasy role playing game. Output a JSON Array directly. NO CODEBLOCK REQUIRED! 重要！JSON对象外面不需要使用\`\`\`json...\`\`\`包围！`,
//             role: "user",
//         },
//     ],
//     output: "array",
//     schema: z.array(
//         z.object({
//             name: z.string(),
//             class: z.string().describe("Character class, e.g. warrior, mage, or thief."),
//             description: z.string(),
//         })
//     ),
// });

// for await (const textPart of textStream) {
//     console.log(textPart);
// }

// for await (const element of elementStream) {
//     console.log(element);
// }
