import { readFileSync } from "fs";
import { randomId } from "koishi";
import Mustache from "mustache";
import path from "path";
import { WorldState } from "../src/services/worldstate/interfaces";

const template = readFileSync(path.resolve(__dirname, "../resources/templates/world_state.mustache"), "utf-8");

const extraData = {
    toString: function (obj): string {
        console.log("toString", obj);
        return typeof obj === "string" ? obj : JSON.stringify(obj);
    },
};

const state: WorldState = {
    timestamp: new Date().toISOString(),
    activeChannels: [
        {
            id: "123",
            name: "123",
            type: "guild",
            platform: "sandbox",
            meta: {},
            members: [],
            memberSummary: {
                total_count: 0,
                online_count: 0,
                recent_active_members_count: 0,
            },
            history: [
                {
                    id: "123",
                    status: "full",
                    events: [],
                    responses: [
                        {
                            thoughts: {
                                obverse: "obverse",
                                analyze_infer: "analyze",
                                plan: "plan",
                            },
                            actions: [
                                {
                                    function: "send",
                                    params: {
                                        message: "parms_message",
                                    },
                                    renderParams() {
                                        return JSON.stringify(this.params);
                                    },
                                },
                            ],
                            observations: [
                                {
                                    function: "send",
                                    result: { success: true, result: "sent" },
                                    renderResult() {
                                        return typeof this.result.result === "string"
                                            ? this.result.result
                                            : JSON.stringify(this.result.result);
                                    },
                                },
                            ],
                        },
                    ],
                },
            ],
        },
    ],
    inactiveChannels: [],
};

const view = { WORLD_STATE: state, ...extraData };
// 禁用 Mustache 的 HTML 转义，因为我们的输出是纯文本
Mustache.escape = (text) => text;
const text = Mustache.render(template, view);

console.log(text);
