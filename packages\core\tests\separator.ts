import { generateText } from "xsai";

import { createOpenAI } from "@xsai-ext/providers-cloud";

const separator = "\u200B\u200C\u200B";

console.time();
const { text } = await generateText({
    messages: [{ role: "user", content: `唐宋八大家都是谁？只输出名字，用\`${separator}\`隔开` }],
    ...createOpenAI(process.env.API_KEY_GOD, "https://api.gptgod.online/v1").chat("deepseek-v3-250324"),
});
console.timeEnd();
const parts = text.split(separator);
console.log("text:", text);
console.log("parts", parts);
