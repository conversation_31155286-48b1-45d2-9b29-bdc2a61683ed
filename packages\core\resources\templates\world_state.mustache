{{#WORLD_STATE}}
<world_state timestamp="{{timestamp}}">
  <active_channels>
    {{#activeChannels}}
    <channel id="{{id}}" type="{{type}}">
      <name>{{name}}</name>
      <members>
        {{#allMembers}}
        <user id="{{id}}"{{#role}} role="{{.}}"{{/role}}>{{name}}</user>
        {{/allMembers}}
      </members>
      <history>
      {{#history}}
        <turn id="{{id}}" status="{{status}}">
          <events>
          {{#events}}
            <event type="{{type}}" timestamp="{{timestamp}}">
              {{! 渲染消息事件 }}
              {{#is_message}}
              <sender id="{{sender.id}}">{{sender.name}}</sender>{{#quote}}{{/quote}}
              <content id="{{messageId}}">{{content}}</content>
              {{/is_message}}
              {{! 渲染成员加入事件 }}
              {{#is_member_join}}
              <user_joined id="{{user_joined.id}}">{{user_joined.name}}</user_joined>
              {{/is_member_join}}
              {{! 渲染用户被禁言事件 }}
              {{#is_user_muted}}
              <muter id="{{muter.id}}">{{muter.name}}</muter>
              <muted_user id="{{muted_user.id}}">{{muted_user.name}}</muted_user>
              <duration_minutes>{{duration_minutes}}</duration_minutes>
              {{#reason}}<reason>{{.}}</reason>{{/reason}}
              {{/is_user_muted}}
              {{! 当你需要添加新事件类型时，比如 member_leave }}
              {{#is_member_leave}}
              {{! ... 在这里添加新事件的渲染逻辑 ... }}
              {{/is_member_leave}}
            </event>
          {{/events}}
          </events>
          <responses>
          {{#responses}}
            <response>
              {{#thoughts}}
              <thoughts>
                <obverse>{{obverse}}</obverse>
                <analyze_infer>{{analyze_infer}}</analyze_infer>
                <plan>{{plan}}</plan>
              </thoughts>
              {{/thoughts}}
              <actions>
              {{#actions}}
                <action>{"function":"{{function}}","params":{{renderParams}}}</action>
              {{/actions}}
              </actions>
              <observations>
              {{#observations}}
                <observation function="{{function}}" status="{{result.status}}">{{renderResult}}</observation>
              {{/observations}}
              </observations>
            </response>
          {{/responses}}
          </responses>
        </turn>
      {{/history}}
      </history>
    </channel>
    {{/activeChannels}}
  </active_channels>
  <inactive_channels>
    {{#inactiveChannels}}
    <channel_id>{{id}}</channel_id>
    {{/inactiveChannels}}
  </inactive_channels>
</world_state>
{{/WORLD_STATE}}