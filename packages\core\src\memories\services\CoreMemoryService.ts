import { MemoryError } from "../../shared/errors/memory.error";
import { CoreMemoryBlockConfig } from "../../types/memory";
import { Context, Logger } from "koishi";
import { MemoryBlock } from "../MemoryBlock";
import { IMemoryBlockStore } from "../stores";

export class CoreMemoryService {
    private coreMemoryBlocks: Map<string, MemoryBlock> = new Map();
    private readonly logger: Logger;

    constructor(private readonly ctx: Context, private readonly store: IMemoryBlockStore) {
        this.logger = ctx.logger(CoreMemoryService.name);
    }

    public async getOrCreateBlock(label: string, customConfig: CoreMemoryBlockConfig = {}): Promise<MemoryBlock> {
        if (this.coreMemoryBlocks.has(label)) {
            const existingBlock = this.coreMemoryBlocks.get(label)!;
            if (customConfig.Limit !== undefined && existingBlock.limit !== customConfig.Limit) {
                this.ctx.logger.warn(`Limit change for existing block ${label} not yet implemented.`);
            }
            return existingBlock;
        }

        const block = await MemoryBlock.getOrCreate(
            this.ctx,
            { label },
            {
                defaultLimit: customConfig.Limit ?? 5000,
                initialValue: customConfig.InitialValue,
                store: this.store,
                filePathToBind: customConfig.FilePathToBind,
            }
        );
        this.coreMemoryBlocks.set(label, block);
        this.logger.debug(`Core memory block "${label}" loaded/created.`);
        return block;
    }

    public getBlock(label: string): MemoryBlock | undefined {
        return this.coreMemoryBlocks.get(label);
    }

    public getBlockOrThrow(label: string): MemoryBlock {
        const block = this.coreMemoryBlocks.get(label);
        if (!block) {
            const available = Array.from(this.coreMemoryBlocks.keys()).join(", ") || "None";
            this.logger.error(`Core memory block "${label}" not found. Available: [${available}]`);
            throw new MemoryError("Core memory block not found", { label, availableLabels: Array.from(this.coreMemoryBlocks.keys()) });
        }
        return block;
    }

    public async appendToBlock(label: string, content: string): Promise<void> {
        const block = this.getBlockOrThrow(label);
        await block.append(content);
        this.logger.info(`Appended to core memory "${label}".`);
    }

    public async replaceInBlock(label: string, oldContent: string, newContent: string): Promise<void> {
        const block = this.getBlockOrThrow(label);
        await block.replace(oldContent, newContent);
        this.logger.info(`Replaced in core memory "${label}".`);
    }

    public async clearBlock(label: string): Promise<void> {
        const block = this.getBlockOrThrow(label);
        await block.clear();
        this.logger.info(`Cleared core memory "${label}".`);
    }

    public getAllBlocks(): MemoryBlock[] {
        return Array.from(this.coreMemoryBlocks.values());
    }

    public async dispose(): Promise<void> {
        for (const block of this.coreMemoryBlocks.values()) {
            await block.disposeFileWatcher().catch((e) => this.logger.warn(`Error disposing watcher for ${block.label}: ${e.message}`));
        }
        this.coreMemoryBlocks.clear();
        this.logger.info("CoreMemoryService disposed.");
    }
}