import { Context } from "koishi";
import {
    MiddlewareDefinition,
    MiddlewareRegistry,
    MiddlewarePriority
} from "../core/MiddlewareCore";
import { ErrorHandlingOptions, RetryConfig, AdapterSwitchingConfig } from "../../types/middleware";

// 导入重构后的中间件实现
import { ErrorHandlingMiddleware } from "../impl/ErrorHandling";
import { DatabaseStorageMiddleware } from "../impl/DatabaseStorage";
import { ReplyConditionMiddleware } from "../impl/ReplyCondition";
import { LLMProcessingMiddleware } from "../impl/LLMProcessing";
import { ResponseHandlingMiddleware } from "../impl/ResponseHandling";

/**
 * 内置中间件标识符
 */
export const BUILTIN_MIDDLEWARE_IDS = {
    ERROR_HANDLING: "builtin.error-handling",
    DATABASE_STORAGE: "builtin.database-storage",
    REPLY_CONDITION: "builtin.reply-condition",
    LLM_PROCESSING: "builtin.llm-processing",
    RESPONSE_HANDLING: "builtin.response-handling"
} as const;

/**
 * 错误处理中间件配置
 */
export interface ErrorHandlingConfig extends ErrorHandlingOptions {
    // 继承原有配置，保持类型一致性
}

/**
 * 数据库存储中间件配置
 */
export interface DatabaseStorageConfig {
    enableImageProcessing?: boolean;
    enableWorldStateUpdate?: boolean;
    batchSize?: number;
    timeout?: number;
}

/**
 * 回复条件中间件配置
 */
export interface ReplyConditionConfig {
    channels: string[][];
    testMode?: boolean;
    strategies: {
        atMention: {
            enabled: boolean;
            probability: number;
        };
        threshold: {
            enabled: boolean;
            value: number;
        };
        conversationFlow: {
            enabled: boolean;
            confidenceThreshold: number;
        };
    };
    timing: {
        waitTime: number;
        sameUserThreshold: number;
    };
    Advanced?: {
        willingness?: {
            messageIncrease: number;
            atIncrease: number;
            decayRate: number;
            retentionAfterReply: number;
            keywords?: {
                list: string[];
                increase: number;
            };
        };
    };
}

/**
 * LLM处理中间件配置
 */
export interface LLMProcessingConfig {
    debug?: boolean;
    retryConfig: RetryConfig;
    adapterSwitchingConfig: AdapterSwitchingConfig;
    timeout?: number;
    enableStreaming?: boolean;
}

/**
 * 响应处理中间件配置
 */
export interface ResponseHandlingConfig {
    maxRetry: number;
    life: number;
    maxHeartbeat: number;
    enableToolValidation?: boolean;
    parallelExecution?: boolean;
}

/**
 * 创建内置中间件定义
 */
export function createBuiltinMiddlewares(): Map<string, MiddlewareDefinition> {
    const definitions = new Map<string, MiddlewareDefinition>();

    // 错误处理中间件
    definitions.set(BUILTIN_MIDDLEWARE_IDS.ERROR_HANDLING, {
        factory: (ctx: Context, config: ErrorHandlingConfig) =>
            new ErrorHandlingMiddleware(ctx, config),
        defaultConfig: {
            debug: false,
            uploadDump: false,
            pasteServiceUrl: "https://dump.yesimbot.chat/",
            includeFullSessionContent: false
        },
        validateConfig: (config: ErrorHandlingConfig) => {
            if (config.pasteServiceUrl && !isValidUrl(config.pasteServiceUrl)) {
                return "pasteServiceUrl 必须是有效的URL";
            }
            return true;
        },
        metadata: {
            id: BUILTIN_MIDDLEWARE_IDS.ERROR_HANDLING,
            name: "错误处理中间件",
            phase: "preprocessing" as any,
            priority: MiddlewarePriority.HIGHEST,
            dependencies: [],
            version: "2.0.0",
            description: "处理中间件链中的错误，提供错误日志记录和上报功能",
            author: "YesImBot Team"
        }
    });

    // 数据库存储中间件
    definitions.set(BUILTIN_MIDDLEWARE_IDS.DATABASE_STORAGE, {
        factory: (ctx: Context, config: DatabaseStorageConfig) =>
            new DatabaseStorageMiddleware(ctx, config),
        defaultConfig: {
            enableImageProcessing: true,
            enableWorldStateUpdate: true,
            batchSize: 10,
            timeout: 5000
        },
        validateConfig: (config: DatabaseStorageConfig) => {
            if (config.batchSize && (config.batchSize < 1 || config.batchSize > 100)) {
                return "batchSize 必须在 1-100 之间";
            }
            if (config.timeout && config.timeout < 1000) {
                return "timeout 不能小于 1000ms";
            }
            return true;
        },
        metadata: {
            id: BUILTIN_MIDDLEWARE_IDS.DATABASE_STORAGE,
            name: "数据库存储中间件",
            phase: "input_processing" as any,
            priority: MiddlewarePriority.HIGH,
            dependencies: [BUILTIN_MIDDLEWARE_IDS.ERROR_HANDLING],
            version: "2.0.0",
            description: "处理消息存储、图片处理和世界状态更新",
            author: "YesImBot Team"
        }
    });

    // 回复条件中间件
    definitions.set(BUILTIN_MIDDLEWARE_IDS.REPLY_CONDITION, {
        factory: (ctx: Context, config: ReplyConditionConfig) =>
            new ReplyConditionMiddleware(ctx, config),
        defaultConfig: {
            channels: [],
            testMode: false,
            strategies: {
                atMention: { enabled: true, probability: 1.0 },
                threshold: { enabled: true, value: 0.7 },
                conversationFlow: { enabled: true, confidenceThreshold: 0.8 }
            },
            timing: {
                waitTime: 1000,
                sameUserThreshold: 5000
            }
        },
        validateConfig: (config: ReplyConditionConfig) => {
            if (config.strategies.atMention.probability < 0 || config.strategies.atMention.probability > 1) {
                return "atMention.probability 必须在 0-1 之间";
            }
            if (config.strategies.threshold.value < 0 || config.strategies.threshold.value > 1) {
                return "threshold.value 必须在 0-1 之间";
            }
            return true;
        },
        metadata: {
            id: BUILTIN_MIDDLEWARE_IDS.REPLY_CONDITION,
            name: "回复条件中间件",
            phase: "condition_check" as any,
            priority: MiddlewarePriority.HIGH,
            dependencies: [BUILTIN_MIDDLEWARE_IDS.DATABASE_STORAGE],
            version: "2.0.0",
            description: "检查是否需要回复消息，支持多种回复策略",
            author: "YesImBot Team"
        }
    });

    // LLM处理中间件
    definitions.set(BUILTIN_MIDDLEWARE_IDS.LLM_PROCESSING, {
        factory: (ctx: Context, config: LLMProcessingConfig) =>
            new LLMProcessingMiddleware(ctx, config),
        defaultConfig: {
            debug: false,
            retryConfig: {
                maxRetries: 3,
                timeoutMs: 30000,
                retryDelayMs: 1000,
                exponentialBackoff: true,
                retryableErrors: ["ECONNREFUSED", "ECONNRESET", "ETIMEDOUT"]
            },
            adapterSwitchingConfig: {
                enabled: true,
                maxAttempts: 3
            },
            timeout: 60000,
            enableStreaming: true
        },
        validateConfig: (config: LLMProcessingConfig) => {
            if (config.retryConfig.maxRetries < 0 || config.retryConfig.maxRetries > 10) {
                return "retryConfig.maxRetries 必须在 0-10 之间";
            }
            if (config.retryConfig.timeoutMs < 1000) {
                return "retryConfig.timeoutMs 不能小于 1000ms";
            }
            return true;
        },
        metadata: {
            id: BUILTIN_MIDDLEWARE_IDS.LLM_PROCESSING,
            name: "LLM处理中间件",
            phase: "core_processing" as any,
            priority: MiddlewarePriority.HIGHEST,
            dependencies: [BUILTIN_MIDDLEWARE_IDS.REPLY_CONDITION],
            version: "2.0.0",
            description: "处理LLM调用，包括重试机制和适配器切换",
            author: "YesImBot Team"
        }
    });

    // 响应处理中间件
    definitions.set(BUILTIN_MIDDLEWARE_IDS.RESPONSE_HANDLING, {
        factory: (ctx: Context, config: ResponseHandlingConfig) =>
            new ResponseHandlingMiddleware(ctx, config),
        defaultConfig: {
            maxRetry: 3,
            life: 3,
            maxHeartbeat: 2,
            enableToolValidation: true,
            parallelExecution: false
        },
        validateConfig: (config: ResponseHandlingConfig) => {
            if (config.maxRetry < 0 || config.maxRetry > 10) {
                return "maxRetry 必须在 0-10 之间";
            }
            if (config.life < 1 || config.life > 10) {
                return "life 必须在 1-10 之间";
            }
            return true;
        },
        metadata: {
            id: BUILTIN_MIDDLEWARE_IDS.RESPONSE_HANDLING,
            name: "响应处理中间件",
            phase: "output_processing" as any,
            priority: MiddlewarePriority.HIGH,
            dependencies: [BUILTIN_MIDDLEWARE_IDS.LLM_PROCESSING],
            version: "2.0.0",
            description: "处理LLM响应，包括工具调用和心跳机制",
            author: "YesImBot Team"
        }
    });

    return definitions;
}

/**
 * 注册所有内置中间件到注册表
 */
export function registerBuiltinMiddlewares(registry: MiddlewareRegistry): void {
    const definitions = createBuiltinMiddlewares();

    for (const [id, definition] of definitions) {
        registry.register(id, definition);
    }
}

/**
 * URL验证辅助函数
 */
function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}
