import { Context } from "koishi";
import { MessageContext } from "../base";

/**
 * 中间件执行阶段
 */
export enum MiddlewarePhase {
    /** 预处理阶段 - 错误处理、日志记录等 */
    PREPROCESSING = "preprocessing",
    /** 输入处理阶段 - 消息解析、存储等 */
    INPUT_PROCESSING = "input_processing",
    /** 条件检查阶段 - 回复条件判断等 */
    CONDITION_CHECK = "condition_check",
    /** 核心处理阶段 - LLM调用、推理等 */
    CORE_PROCESSING = "core_processing",
    /** 输出处理阶段 - 响应生成、工具调用等 */
    OUTPUT_PROCESSING = "output_processing",
    /** 后处理阶段 - 清理、统计等 */
    POSTPROCESSING = "postprocessing"
}

/**
 * 中间件优先级
 */
export enum MiddlewarePriority {
    HIGHEST = 1000,
    HIGH = 800,
    NORMAL = 500,
    LOW = 200,
    LOWEST = 100
}

/**
 * 中间件状态
 */
export enum MiddlewareStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    SKIPPED = "skipped"
}

/**
 * 增强的中间件上下文
 */
export interface MiddlewareContext extends MessageContext {
    /** 中间件执行元数据 */
    readonly metadata: {
        /** 当前执行阶段 */
        currentPhase: MiddlewarePhase;
        /** 执行开始时间 */
        startTime: number;
        /** 已执行的中间件列表 */
        executedMiddlewares: string[];
        /** 中间件执行状态 */
        middlewareStates: Map<string, MiddlewareStatus>;
        /** 性能指标 */
        performance: Map<string, number>;
    };

    /** 中间件间共享数据 */
    readonly shared: Map<string, any>;

    /** 跳过后续中间件 */
    skip(reason?: string): void;

    /** 标记中间件执行失败 */
    fail(error: Error, middlewareName: string): void;

    /** 获取性能指标 */
    getPerformanceMetrics(): Record<string, number>;
}

/**
 * 中间件接口
 */
export interface IMiddleware<TConfig = any> {
    /** 中间件唯一标识 */
    readonly id: string;
    /** 中间件名称 */
    readonly name: string;
    /** 执行阶段 */
    readonly phase: MiddlewarePhase;
    /** 优先级 */
    readonly priority: MiddlewarePriority;
    /** 是否启用 */
    readonly enabled: boolean;
    /** 依赖的中间件ID列表 */
    readonly dependencies: string[];
    /** 配置对象 */
    readonly config: TConfig;

    /** 执行中间件逻辑 */
    execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void>;

    /** 初始化中间件 */
    initialize?(): Promise<void>;

    /** 清理资源 */
    dispose?(): Promise<void>;

    /** 健康检查 */
    healthCheck?(): Promise<boolean>;

    /** 获取中间件信息 */
    getInfo(): MiddlewareInfo;
}

/**
 * 中间件信息
 */
export interface MiddlewareInfo {
    id: string;
    name: string;
    phase: MiddlewarePhase;
    priority: MiddlewarePriority;
    enabled: boolean;
    dependencies: string[];
    version?: string;
    description?: string;
    author?: string;
}

/**
 * 中间件定义
 */
export interface MiddlewareDefinition<TConfig = any> {
    /** 中间件工厂函数 */
    factory: MiddlewareFactory<TConfig>;
    /** 默认配置 */
    defaultConfig?: TConfig;
    /** 配置验证函数 */
    validateConfig?: (config: TConfig) => boolean | string;
    /** 中间件元信息 */
    metadata: Omit<MiddlewareInfo, 'enabled'>;
}

/**
 * 中间件工厂函数
 */
export type MiddlewareFactory<TConfig = any> = (
    ctx: Context,
    config: TConfig
) => IMiddleware<TConfig>;

/**
 * 中间件注册表
 */
export class MiddlewareRegistry {
    private definitions = new Map<string, MiddlewareDefinition>();
    private instances = new Map<string, IMiddleware>();

    constructor(private ctx: Context) {}

    /**
     * 注册中间件定义
     */
    register<TConfig = any>(
        id: string,
        definition: MiddlewareDefinition<TConfig>
    ): void {
        if (this.definitions.has(id)) {
            throw new Error(`中间件 ${id} 已经注册`);
        }

        this.definitions.set(id, definition);
        this.ctx.logger.debug(`中间件 ${id} 注册成功`);
    }

    /**
     * 创建中间件实例
     */
    create<TConfig = any>(id: string, config?: TConfig): IMiddleware<TConfig> {
        const definition = this.definitions.get(id);
        if (!definition) {
            throw new Error(`未找到中间件定义: ${id}`);
        }

        const finalConfig = { ...definition.defaultConfig, ...config };

        // 验证配置
        if (definition.validateConfig && !definition.validateConfig(finalConfig)) {
            throw new Error(`中间件 ${id} 配置验证失败`);
        }

        const instance = definition.factory(this.ctx, finalConfig);
        this.instances.set(id, instance);

        return instance;
    }

    /**
     * 获取中间件实例
     */
    get(id: string): IMiddleware | undefined {
        return this.instances.get(id);
    }

    /**
     * 获取所有注册的中间件定义
     */
    getDefinitions(): Map<string, MiddlewareDefinition> {
        return new Map(this.definitions);
    }

    /**
     * 获取所有中间件实例
     */
    getInstances(): Map<string, IMiddleware> {
        return new Map(this.instances);
    }

    /**
     * 清理所有实例
     */
    async dispose(): Promise<void> {
        for (const [id, instance] of this.instances) {
            if (instance.dispose) {
                try {
                    await instance.dispose();
                } catch (error) {
                    this.ctx.logger.error(`清理中间件 ${id} 失败:`, error);
                }
            }
        }
        this.instances.clear();
    }
}

/**
 * 中间件管道
 */
export class MiddlewarePipeline {
    private middlewares: IMiddleware[] = [];
    private phaseMap = new Map<MiddlewarePhase, IMiddleware[]>();
    private isBuilt = false;

    constructor(
        private ctx: Context,
        private registry: MiddlewareRegistry
    ) {}

    /**
     * 添加中间件到管道
     */
    add(middlewareId: string, config?: any): this {
        const middleware = this.registry.create(middlewareId, config);
        this.middlewares.push(middleware);
        this.isBuilt = false;
        return this;
    }

    /**
     * 构建执行管道
     */
    async build(): Promise<void> {
        if (this.isBuilt) return;

        // 1. 依赖检查和排序
        this.validateDependencies();
        this.sortMiddlewares();

        // 2. 按阶段分组
        this.groupByPhase();

        // 3. 初始化所有中间件
        await this.initializeMiddlewares();

        this.isBuilt = true;
        this.ctx.logger.info(`中间件管道构建完成，共 ${this.middlewares.length} 个中间件`);
    }

    /**
     * 执行中间件管道
     */
    async execute(messageContext: MessageContext): Promise<void> {
        if (!this.isBuilt) {
            throw new Error("中间件管道未构建，请先调用 build()");
        }

        const ctx = this.createMiddlewareContext(messageContext);

        try {
            await this.executePhases(ctx);
        } catch (error) {
            ctx.fail(error as Error, "pipeline");
            throw error;
        }
    }

    /**
     * 创建中间件上下文
     */
    private createMiddlewareContext(messageContext: MessageContext): MiddlewareContext {
        const shared = new Map<string, any>();
        let skipped = false;
        let skipReason: string | undefined;

        const middlewareContext: MiddlewareContext = {
            ...messageContext,
            metadata: {
                currentPhase: MiddlewarePhase.PREPROCESSING,
                startTime: Date.now(),
                executedMiddlewares: [],
                middlewareStates: new Map(),
                performance: new Map()
            },
            shared,
            skip: (reason?: string) => {
                skipped = true;
                skipReason = reason;
            },
            fail: (error: Error, middlewareName: string) => {
                middlewareContext.metadata.middlewareStates.set(middlewareName, MiddlewareStatus.FAILED);
                this.ctx.logger.error(`中间件 ${middlewareName} 执行失败:`, error);
            },
            getPerformanceMetrics: () => {
                return Object.fromEntries(middlewareContext.metadata.performance);
            }
        };

        return middlewareContext;
    }

    /**
     * 按阶段执行中间件
     */
    private async executePhases(ctx: MiddlewareContext): Promise<void> {
        const phases = Object.values(MiddlewarePhase);

        for (const phase of phases) {
            ctx.metadata.currentPhase = phase;
            const phaseMiddlewares = this.phaseMap.get(phase) || [];

            if (phaseMiddlewares.length > 0) {
                await this.executePhaseMiddlewares(ctx, phaseMiddlewares);
            }
        }
    }

    /**
     * 执行特定阶段的中间件
     */
    private async executePhaseMiddlewares(
        ctx: MiddlewareContext,
        middlewares: IMiddleware[]
    ): Promise<void> {
        let index = 0;

        const next = async (): Promise<void> => {
            if (index >= middlewares.length) return;

            const middleware = middlewares[index++];
            if (!middleware.enabled) {
                ctx.metadata.middlewareStates.set(middleware.id, MiddlewareStatus.SKIPPED);
                return next();
            }

            const startTime = Date.now();
            ctx.metadata.middlewareStates.set(middleware.id, MiddlewareStatus.RUNNING);

            try {
                await middleware.execute(ctx, next);

                const duration = Date.now() - startTime;
                ctx.metadata.performance.set(middleware.id, duration);
                ctx.metadata.executedMiddlewares.push(middleware.id);
                ctx.metadata.middlewareStates.set(middleware.id, MiddlewareStatus.COMPLETED);

            } catch (error) {
                ctx.fail(error as Error, middleware.id);
                throw error;
            }
        };

        await next();
    }

    /**
     * 验证依赖关系
     */
    private validateDependencies(): void {
        const middlewareIds = new Set(this.middlewares.map(m => m.id));

        for (const middleware of this.middlewares) {
            for (const dep of middleware.dependencies) {
                if (!middlewareIds.has(dep)) {
                    throw new Error(`中间件 ${middleware.id} 依赖的中间件 ${dep} 未找到`);
                }
            }
        }
    }

    /**
     * 按优先级和依赖关系排序中间件
     */
    private sortMiddlewares(): void {
        // 拓扑排序 + 优先级排序
        this.middlewares.sort((a, b) => {
            // 首先按阶段排序
            if (a.phase !== b.phase) {
                return Object.values(MiddlewarePhase).indexOf(a.phase) -
                       Object.values(MiddlewarePhase).indexOf(b.phase);
            }
            // 然后按优先级排序
            return b.priority - a.priority;
        });
    }

    /**
     * 按阶段分组中间件
     */
    private groupByPhase(): void {
        this.phaseMap.clear();

        for (const middleware of this.middlewares) {
            const phaseMiddlewares = this.phaseMap.get(middleware.phase) || [];
            phaseMiddlewares.push(middleware);
            this.phaseMap.set(middleware.phase, phaseMiddlewares);
        }
    }

    /**
     * 初始化所有中间件
     */
    private async initializeMiddlewares(): Promise<void> {
        for (const middleware of this.middlewares) {
            if (middleware.initialize) {
                try {
                    await middleware.initialize();
                } catch (error) {
                    this.ctx.logger.error(`初始化中间件 ${middleware.id} 失败:`, error);
                    throw error;
                }
            }
        }
    }

    /**
     * 获取管道信息
     */
    getInfo(): {
        middlewareCount: number;
        phaseDistribution: Record<MiddlewarePhase, number>;
        middlewares: MiddlewareInfo[];
    } {
        const phaseDistribution = {} as Record<MiddlewarePhase, number>;
        Object.values(MiddlewarePhase).forEach(phase => {
            phaseDistribution[phase] = this.phaseMap.get(phase)?.length || 0;
        });

        return {
            middlewareCount: this.middlewares.length,
            phaseDistribution,
            middlewares: this.middlewares.map(m => m.getInfo())
        };
    }

    /**
     * 清理管道
     */
    async dispose(): Promise<void> {
        for (const middleware of this.middlewares) {
            if (middleware.dispose) {
                try {
                    await middleware.dispose();
                } catch (error) {
                    this.ctx.logger.error(`清理中间件 ${middleware.id} 失败:`, error);
                }
            }
        }
        this.middlewares = [];
        this.phaseMap.clear();
        this.isBuilt = false;
    }
}
