import { Schema } from "koishi";
import { ChatConfig } from "../../types/chat";

export const ChatConfigSchema: Schema<ChatConfig> = Schema.object({
    UseModel: Schema.array(
        Schema.object({ providerName: Schema.string(), modelId: Schema.string() }).description("第几个提供商的第几个模型，从 0 开始计数")
    )
        .required()
        .description("对话使用的模型"),
    MaxHeartbeat: Schema.number().min(1).max(6).default(2).step(1).role("slider").description("最大心跳次数，控制对话的活跃度"),
    WordsPerSecond: Schema.number().min(0).max(360).default(20).step(1).role("slider").description("模拟打字速度，每秒发送的字符数"),
}).description("对话行为配置");
