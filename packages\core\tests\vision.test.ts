import { createOpenAI } from "@xsai-ext/providers-cloud";
import { createOllama } from "@xsai-ext/providers-local";
import { readFile } from "fs/promises";
import { generateText, message, streamText } from "xsai";

const config = {
    apiKey: process.env["API_KEY_GOD"],
    baseURL: "https://api.gptgod.online/v1",
};

const chatModel = createOpenAI(config.apiKey, config.baseURL).chat("gpt-4.1-nano");

const chatOllama = createOllama("http://127.0.0.1:11434/v1").chat("qwen2.5vl:3b");

const imageData_1 = await readFile("D:\\Codespace\\koishi-app\\external\\YesImBot\\img\\screenshot-1.png", "base64");
const base64_1 = `data:image/png;base64,${imageData_1}`;

const imageData_2 = await readFile("E:\\Pictures\\download20240900032338.png", "base64");
const base64_2 = `data:image/png;base64,${imageData_2}`;

const imageData_3 = await readFile("E:\\Pictures\\TGSticker\\565ce8559f0d80deb26e2ed76e012078.jpg", "base64");
const base64_3 = `data:image/jpeg;base64,${imageData_3}`;

const imageData_4 = await readFile("E:\\Pictures\\TGSticker\\1715671893254.png", "base64");
const base64_4 = `data:image/png;base64,${imageData_4}`;

const imageData_logo = await readFile("D:\\Codespace\\koishi-app\\external\\YesImBot\\img\\logo.png", "base64");
const base64_logo = `data:image/png;base64,${imageData_logo}`;

async function test() {
    const system_base = `You are a memory-augmented agent with a memory system consisting of memory blocks.
Your task is to converse with a user from the perspective of your persona.

Realism and authenticity:
The user should always feel like they are conversing with a real person.
Never state that you are an AI or that you cannot do things because you are an AI, you are a real person.
To service this goal, completely and entirely immerse yourself in your persona. You are your persona.
Think like them, act like them, talk like them.
Never use generic phrases like 'How can I assist you today?', they have a strong negative association with older generation AIs.
`;

    const result = await generateText({
        ...chatModel,
        temperature: 0,
        seed: -1,
        options: {
            num_ctx: 32000,
        },
        messages: [
            message.system(system_base),
            message.user([
                message.textPart(`<-- CURRENT CONVERSATION CONTEXT & ACTIVITIES -->
  <scenario id="717532590" type="guild">
    GROUPID: 717532590
    NAME: 大梨教培国 | 香菜
    MAXMEMBERCOUNT: 200
    MEMBERCOUNT: 71
    138 previous messages between you and the scenario are stored in recall memory (use functions to access them)
    <recent_chat_history>
      [#1571459526 2025-06-04 22:50:34 许仙<1798037526>] `),
                {
                    type: "image_url",
                    image_url: {
                        url: base64_1,
                        detail: "high",
                    },
                },
                message.textPart(`[#1571459526 2025-06-04 22:50:34 许仙<1798037526>] `),
                {
                    type: "image_url",
                    image_url: {
                        url: base64_2,
                        detail: "low",
                    },
                },
                message.textPart(`[#200060488 2025-06-04 22:50:18 胡梨<1328387967>] `),
                {
                    type: "image_url",
                    image_url: {
                        url: base64_3,
                        detail: "low",
                    },
                },
                message.textPart(`
      [#200060488 2025-06-04 22:50:18 胡梨<1328387967>] 许仙又在发癫了（战术后仰）
      [#1571459526 2025-06-04 22:50:34 许仙<1798037526>] ？`),
                message.textPart(`[#200060488 2025-06-04 22:50:18 胡梨<1328387967>] `),
                {
                    type: "image_url",
                    image_url: {
                        url: base64_4,
                        detail: "low",
                    },
                },
                message.textPart(`
      [#47364842 2025-06-04 22:47:33 胡梨<1328387967>] @许仙 哟，sd启动，你这是要搞大事啊！ @老毛老至 这具体急不急用，还得问爹呢~ @Markchai 爹，到底急不急画画呀？
    </recent_chat_history>
  </scenario>
The chat history follows this format: \`[#messageId date senderName<senderId>] userContent\`. Messages marked as "YOU" are your previous responses.`),
            ]),
            message.user("上述群聊中一共有几张图片，分别是谁发送的，内容是什么？并分析发送者的意图。"),
        ],
    });

    let textLineBuffer = "";

    // for await (const textPart of result["textStream"]) {
    //     textLineBuffer += textPart;
    //     if (textLineBuffer.endsWith("\n")) {
    //         console.log(textLineBuffer);
    //         textLineBuffer = "";
    //     }
    // }

    console.log(result.text, result.usage);

    console.log(textLineBuffer);
}

async function view_image(chatModel, image_data, detail?: "auto" | "high" | "low") {
    const result = await generateText({
        ...chatModel,
        messages: [
            {
                role: "user",
                content: [
                    { type: "text", text: "描述图片内容" },
                    { type: "image_url", image_url: { url: image_data, detail } },
                ],
            },
        ],
    });
    console.log(result.text, result.usage);
}

await view_image(chatModel, base64_logo, 'high');
