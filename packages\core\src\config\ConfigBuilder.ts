import { SimpleConfig, ConfigValidator } from "./SimpleConfig";

/**
 * 配置构建器
 * 提供流式API来构建配置，减少配置复杂度
 */
export class ConfigBuilder {
    private config: Partial<SimpleConfig> = {};

    /**
     * 设置重试配置
     */
    retry(options: {
        maxRetries?: number;
        timeoutMs?: number;
        delayMs?: number;
        exponentialBackoff?: boolean;
        retryableErrors?: string[];
    }): this {
        this.config.maxRetries = options.maxRetries ?? 3;
        this.config.retryTimeoutMs = options.timeoutMs ?? 30000;
        this.config.retryDelayMs = options.delayMs ?? 1000;
        this.config.enableExponentialBackoff = options.exponentialBackoff ?? true;
        this.config.retryableErrors = options.retryableErrors ?? [];
        return this;
    }

    /**
     * 设置适配器切换配置
     */
    adapterSwitching(enabled: boolean, maxAttempts: number = 3): this {
        this.config.enableAdapterSwitching = enabled;
        this.config.maxAdapterAttempts = maxAttempts;
        return this;
    }

    /**
     * 设置记忆块配置
     */
    memoryBlock(name: string, limit: number, filePath: string): this {
        if (!this.config.memoryBlocks) {
            this.config.memoryBlocks = {};
        }
        this.config.memoryBlocks[name] = { limit, filePath };
        return this;
    }

    /**
     * 设置压缩配置
     */
    compression(trigger: "lines" | "characters" | "interval", threshold: number, customPrompt?: string): this {
        this.config.compressionTrigger = trigger;
        this.config.compressionThreshold = threshold;
        this.config.compressionPrompt = customPrompt;
        return this;
    }

    /**
     * 设置工具配置
     */
    tools(maxRetries: number = 3, lifetime: number = 3): this {
        this.config.maxToolRetries = maxRetries;
        this.config.toolLifetime = lifetime;
        return this;
    }

    /**
     * 设置调试配置
     */
    debug(enabled: boolean, uploadDumps: boolean = false): this {
        this.config.enableDebug = enabled;
        this.config.uploadErrorDumps = uploadDumps;
        return this;
    }

    /**
     * 设置聊天配置
     */
    chat(maxHeartbeats: number = 2, typingSpeed: number = 20): this {
        this.config.maxHeartbeats = maxHeartbeats;
        this.config.typingSpeed = typingSpeed;
        return this;
    }

    /**
     * 构建配置
     */
    build(): SimpleConfig {
        const validation = ConfigValidator.validate(this.config);
        if (!validation.isValid) {
            throw new Error(`配置验证失败: ${validation.errors.join(", ")}`);
        }
        
        return this.config as SimpleConfig;
    }

    /**
     * 从现有配置创建构建器
     */
    static from(config: Partial<SimpleConfig>): ConfigBuilder {
        const builder = new ConfigBuilder();
        builder.config = { ...config };
        return builder;
    }
}

/**
 * 预设配置
 */
export class ConfigPresets {
    /**
     * 开发环境配置
     */
    static development(): ConfigBuilder {
        return new ConfigBuilder()
            .debug(true, false)
            .retry({ maxRetries: 1, timeoutMs: 10000 })
            .adapterSwitching(false)
            .compression("lines", 50)
            .tools(1, 1);
    }

    /**
     * 生产环境配置
     */
    static production(): ConfigBuilder {
        return new ConfigBuilder()
            .debug(false, true)
            .retry({ maxRetries: 3, timeoutMs: 30000, exponentialBackoff: true })
            .adapterSwitching(true, 3)
            .compression("lines", 100)
            .tools(3, 3);
    }

    /**
     * 测试环境配置
     */
    static testing(): ConfigBuilder {
        return new ConfigBuilder()
            .debug(true, false)
            .retry({ maxRetries: 0, timeoutMs: 5000 })
            .adapterSwitching(false)
            .compression("lines", 10)
            .tools(0, 1);
    }
}

/**
 * 配置工厂
 */
export class ConfigFactory {
    /**
     * 创建默认配置
     */
    static createDefault(): SimpleConfig {
        return ConfigPresets.production().build();
    }

    /**
     * 根据环境创建配置
     */
    static createForEnvironment(env: "development" | "production" | "testing"): SimpleConfig {
        switch (env) {
            case "development":
                return ConfigPresets.development().build();
            case "production":
                return ConfigPresets.production().build();
            case "testing":
                return ConfigPresets.testing().build();
            default:
                return this.createDefault();
        }
    }

    /**
     * 合并配置
     */
    static merge(base: Partial<SimpleConfig>, override: Partial<SimpleConfig>): SimpleConfig {
        const merged = { ...base, ...override };
        
        // 深度合并记忆块配置
        if (base.memoryBlocks && override.memoryBlocks) {
            merged.memoryBlocks = { ...base.memoryBlocks, ...override.memoryBlocks };
        }
        
        const validation = ConfigValidator.validate(merged);
        if (!validation.isValid) {
            throw new Error(`合并后的配置验证失败: ${validation.errors.join(", ")}`);
        }
        
        return merged as SimpleConfig;
    }
}
