import { Context } from "koishi";
import { Config } from "../src/config";
import {
    MiddlewareRegistry,
    MiddlewarePipeline,
    MiddlewarePhase,
    MiddlewarePriority,
    MiddlewareContext,
} from "../src/middlewares/core/MiddlewareCore";
import {
    BaseMiddleware,
    PreprocessingMiddleware,
    PostprocessingMiddleware,
    middleware,
    validateConfig,
} from "../src/middlewares/core/BaseMiddleware";
import { AdvancedMiddlewareConfigurator } from "../src/services/SimpleMiddlewareConfigurator";
import { SimpleServiceManager } from "../src/services/SimpleServiceManager";

/**
 * 自定义中间件示例 1：性能监控中间件
 */
interface PerformanceMonitorConfig {
    enableDetailedMetrics: boolean;
    slowThresholdMs: number;
    reportInterval: number;
}

@middleware({
    id: "custom.performance-monitor",
    name: "性能监控中间件",
    phase: MiddlewarePhase.PREPROCESSING,
    priority: MiddlewarePriority.HIGH,
})
@validateConfig<PerformanceMonitorConfig>((config) => {
    if (config.slowThresholdMs < 100) {
        return "slowThresholdMs 不能小于 100ms";
    }
    return true;
})
class PerformanceMonitorMiddleware extends PreprocessingMiddleware<PerformanceMonitorConfig> {
    private metrics = new Map<string, number[]>();
    private reportTimer?: NodeJS.Timeout;

    constructor(ctx: Context, config: PerformanceMonitorConfig) {
        super("custom.performance-monitor", "性能监控中间件", ctx, config, { priority: MiddlewarePriority.HIGH });
    }

    async initialize(): Promise<void> {
        if (this.config.reportInterval > 0) {
            this.reportTimer = setInterval(() => {
                this.reportMetrics();
            }, this.config.reportInterval);
        }
        this.logger.info("性能监控中间件初始化完成");
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        const startTime = Date.now();

        try {
            await next();
        } finally {
            const duration = Date.now() - startTime;
            this.recordPerformance(ctx, duration);

            if (duration > this.config.slowThresholdMs) {
                this.logger.warn(`慢请求检测: ${duration}ms (阈值: ${this.config.slowThresholdMs}ms)`);
            }
        }
    }

    private recordPerformance(ctx: MiddlewareContext, duration: number): void {
        const key = `${ctx.koishiSession.platform}:${ctx.koishiSession.channelId}`;
        const metrics = this.metrics.get(key) || [];
        metrics.push(duration);

        // 保留最近100条记录
        if (metrics.length > 100) {
            metrics.shift();
        }

        this.metrics.set(key, metrics);
        this.recordMetric(ctx, "total_duration", duration);
    }

    private reportMetrics(): void {
        if (this.config.enableDetailedMetrics) {
            for (const [key, metrics] of this.metrics) {
                const avg = metrics.reduce((a, b) => a + b, 0) / metrics.length;
                const max = Math.max(...metrics);
                const min = Math.min(...metrics);

                this.logger.info(`性能报告 [${key}]: 平均=${avg.toFixed(2)}ms, 最大=${max}ms, 最小=${min}ms`);
            }
        }
    }

    async dispose(): Promise<void> {
        if (this.reportTimer) {
            clearInterval(this.reportTimer);
        }
        this.logger.info("性能监控中间件已清理");
    }
}

/**
 * 自定义中间件示例 2：内容过滤中间件
 */
interface ContentFilterConfig {
    enableProfanityFilter: boolean;
    enableSpamFilter: boolean;
    blockedWords: string[];
    maxMessageLength: number;
}

@middleware({
    id: "custom.content-filter",
    name: "内容过滤中间件",
    phase: MiddlewarePhase.INPUT_PROCESSING,
    priority: MiddlewarePriority.NORMAL,
})
class ContentFilterMiddleware extends BaseMiddleware<ContentFilterConfig> {
    constructor(ctx: Context, config: ContentFilterConfig) {
        super("custom.content-filter", "内容过滤中间件", MiddlewarePhase.INPUT_PROCESSING, ctx, config, {
            priority: MiddlewarePriority.NORMAL,
        });
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        const content = ctx.koishiSession.content;

        // 检查消息长度
        if (content.length > this.config.maxMessageLength) {
            this.logger.warn(`消息过长: ${content.length} > ${this.config.maxMessageLength}`);
            ctx.skip("消息过长");
            return;
        }

        // 检查违禁词
        if (this.config.enableProfanityFilter && this.containsBlockedWords(content)) {
            this.logger.warn("检测到违禁词，跳过处理");
            ctx.skip("包含违禁词");
            return;
        }

        // 检查垃圾信息
        if (this.config.enableSpamFilter && this.isSpam(content)) {
            this.logger.warn("检测到垃圾信息，跳过处理");
            ctx.skip("垃圾信息");
            return;
        }

        await next();
    }

    private containsBlockedWords(content: string): boolean {
        const lowerContent = content.toLowerCase();
        return this.config.blockedWords.some((word) => lowerContent.includes(word.toLowerCase()));
    }

    private isSpam(content: string): boolean {
        // 简单的垃圾信息检测逻辑
        const repeatedChars = /(.)\1{10,}/.test(content);
        const tooManyEmojis = (content.match(/[\u{1F600}-\u{1F64F}]/gu) || []).length > 10;

        return repeatedChars || tooManyEmojis;
    }
}

/**
 * 自定义中间件示例 3：统计收集中间件
 */
interface StatisticsConfig {
    enableUserStats: boolean;
    enableChannelStats: boolean;
    enableCommandStats: boolean;
}

@middleware({
    id: "custom.statistics",
    name: "统计收集中间件",
    phase: MiddlewarePhase.POSTPROCESSING,
    priority: MiddlewarePriority.LOW,
})
class StatisticsMiddleware extends PostprocessingMiddleware<StatisticsConfig> {
    private userStats = new Map<string, { messageCount: number; lastActive: number }>();
    private channelStats = new Map<string, { messageCount: number; userCount: Set<string> }>();

    constructor(ctx: Context, config: StatisticsConfig) {
        super("custom.statistics", "统计收集中间件", ctx, config, { priority: MiddlewarePriority.LOW });
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        await next();

        // 收集统计信息
        if (this.config.enableUserStats) {
            this.updateUserStats(ctx);
        }

        if (this.config.enableChannelStats) {
            this.updateChannelStats(ctx);
        }

        if (this.config.enableCommandStats) {
            this.updateCommandStats(ctx);
        }
    }

    private updateUserStats(ctx: MiddlewareContext): void {
        const userId = ctx.koishiSession.userId;
        const stats = this.userStats.get(userId) || { messageCount: 0, lastActive: 0 };

        stats.messageCount++;
        stats.lastActive = Date.now();

        this.userStats.set(userId, stats);
    }

    private updateChannelStats(ctx: MiddlewareContext): void {
        const channelId = ctx.koishiSession.channelId;
        const userId = ctx.koishiSession.userId;

        const stats = this.channelStats.get(channelId) || {
            messageCount: 0,
            userCount: new Set<string>(),
        };

        stats.messageCount++;
        stats.userCount.add(userId);

        this.channelStats.set(channelId, stats);
    }

    private updateCommandStats(ctx: MiddlewareContext): void {
        // 检查是否是命令消息
        const content = ctx.koishiSession.content;
        if (content.startsWith("/") || content.startsWith("!")) {
            const command = content.split(" ")[0];
            this.recordMetric(ctx, `command.${command}`, 1);
        }
    }

    getStats(): {
        users: Record<string, any>;
        channels: Record<string, any>;
    } {
        return {
            users: Object.fromEntries(this.userStats),
            channels: Object.fromEntries(
                Array.from(this.channelStats.entries()).map(([id, stats]) => [id, { ...stats, userCount: stats.userCount.size }])
            ),
        };
    }
}

/**
 * 使用示例：创建带有自定义中间件的 Agent
 */
export async function createAdvancedAgent(ctx: Context, config: Config): Promise<void> {
    // 1. 创建服务管理器
    const services = new SimpleServiceManager(ctx, config);

    // 2. 创建中间件注册表和管道
    const registry = new MiddlewareRegistry(ctx);
    const pipeline = new MiddlewarePipeline(ctx, registry);

    // 3. 注册自定义中间件
    registry.register("custom.performance-monitor", {
        factory: (ctx, config) => new PerformanceMonitorMiddleware(ctx, config),
        defaultConfig: {
            enableDetailedMetrics: true,
            slowThresholdMs: 1000,
            reportInterval: 60000,
        },
        metadata: {
            id: "custom.performance-monitor",
            name: "性能监控中间件",
            phase: MiddlewarePhase.PREPROCESSING,
            priority: MiddlewarePriority.HIGH,
            dependencies: [],
            version: "1.0.0",
            description: "监控中间件执行性能",
            author: "Custom Developer",
        },
    });

    registry.register("custom.content-filter", {
        factory: (ctx, config) => new ContentFilterMiddleware(ctx, config),
        defaultConfig: {
            enableProfanityFilter: true,
            enableSpamFilter: true,
            blockedWords: ["spam", "advertisement"],
            maxMessageLength: 2000,
        },
        metadata: {
            id: "custom.content-filter",
            name: "内容过滤中间件",
            phase: MiddlewarePhase.INPUT_PROCESSING,
            priority: MiddlewarePriority.NORMAL,
            dependencies: [],
            version: "1.0.0",
            description: "过滤不当内容和垃圾信息",
            author: "Custom Developer",
        },
    });

    registry.register("custom.statistics", {
        factory: (ctx, config) => new StatisticsMiddleware(ctx, config),
        defaultConfig: {
            enableUserStats: true,
            enableChannelStats: true,
            enableCommandStats: true,
        },
        metadata: {
            id: "custom.statistics",
            name: "统计收集中间件",
            phase: MiddlewarePhase.POSTPROCESSING,
            priority: MiddlewarePriority.LOW,
            dependencies: [],
            version: "1.0.0",
            description: "收集用户和频道统计信息",
            author: "Custom Developer",
        },
    });

    // 4. 使用配置器构建完整的中间件管道
    const configurator = new AdvancedMiddlewareConfigurator(ctx, config, services);
    const builtPipeline = await configurator.configure();

    // 5. 添加自定义中间件
    builtPipeline.add("custom.performance-monitor").add("custom.content-filter").add("custom.statistics");

    // 6. 重新构建管道
    await builtPipeline.build();

    // 7. 获取管道信息
    const pipelineInfo = builtPipeline.getInfo();
    ctx.logger.info("中间件管道构建完成:", pipelineInfo);

    // 8. 注册消息处理器
    ctx.middleware(async (session, next) => {
        try {
            const messageContext = await ctx.createMessageContext(session);
            await builtPipeline.execute(messageContext);
        } catch (error) {
            ctx.logger.error("中间件管道执行失败:", error);
        }
        return next();
    });
}

export { PerformanceMonitorMiddleware, ContentFilterMiddleware, StatisticsMiddleware };
