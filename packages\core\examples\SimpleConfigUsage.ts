import { Context } from "koishi";
import { ConfigBuilder, ConfigPresets, ConfigFactory } from "../src/config/ConfigBuilder";
import { SimpleAgent, SimpleAgentBuilder } from "../src/SimpleAgent";

/**
 * 简化配置使用示例
 */

// 示例 1: 使用配置构建器
function createAgentWithBuilder(ctx: Context): SimpleAgent {
    const config = new ConfigBuilder()
        .debug(true)
        .retry({ maxRetries: 3, timeoutMs: 30000 })
        .adapterSwitching(true, 3)
        .memoryBlock("user_info", 5000, "data/memory/user.txt")
        .memoryBlock("conversation", 10000, "data/memory/conversation.txt")
        .compression("lines", 100)
        .tools(3, 5)
        .chat(2, 20)
        .build();

    return new SimpleAgent(ctx, config);
}

// 示例 2: 使用预设配置
function createAgentWithPreset(ctx: Context): SimpleAgent {
    const config = ConfigPresets.development()
        .memoryBlock("custom", 2000, "data/custom.txt")
        .build();

    return new SimpleAgent(ctx, config);
}

// 示例 3: 使用 Agent 构建器
function createAgentWithAgentBuilder(ctx: Context): SimpleAgent {
    return new SimpleAgentBuilder(ctx)
        .withDebug(true)
        .withRetry(3, 30000)
        .withConfig({
            memoryBlocks: {
                "user": { limit: 5000, filePath: "data/user.txt" },
                "system": { limit: 2000, filePath: "data/system.txt" }
            },
            compressionTrigger: "lines",
            compressionThreshold: 100
        })
        .build();
}

// 示例 4: 环境特定配置
function createAgentForEnvironment(ctx: Context, env: string): SimpleAgent {
    const config = ConfigFactory.createForEnvironment(env as any);
    return new SimpleAgent(ctx, config);
}

// 示例 5: 配置合并
function createAgentWithMergedConfig(ctx: Context): SimpleAgent {
    const baseConfig = ConfigPresets.production().build();
    const customConfig = {
        enableDebug: true,
        memoryBlocks: {
            "custom": { limit: 1000, filePath: "data/custom.txt" }
        }
    };
    
    const mergedConfig = ConfigFactory.merge(baseConfig, customConfig);
    return new SimpleAgent(ctx, mergedConfig);
}

/**
 * 对比：原始复杂配置 vs 简化配置
 */

// 原始复杂配置（嵌套深度大）
const complexConfig = {
    LLM: {
        RetryConfig: {
            MaxRetries: 3,
            TimeoutMs: 30000,
            RetryDelayMs: 1000,
            ExponentialBackoff: true,
            RetryableErrors: ["timeout", "rate_limit"]
        },
        AdapterSwitching: {
            Enabled: true,
            MaxAttempts: 3
        }
    },
    Memory: {
        Block: {
            user: { Limit: 5000, FilePathToBind: "data/user.txt" }
        },
        Compression: {
            CompressionWhen: "Lines",
            Lines: 100,
            CustomPrompt: "Compress this content..."
        }
    },
    Debug: {
        EnableDebug: true,
        UploadDump: false
    }
};

// 简化配置（扁平化）
const simpleConfig = {
    maxRetries: 3,
    retryTimeoutMs: 30000,
    retryDelayMs: 1000,
    enableExponentialBackoff: true,
    retryableErrors: ["timeout", "rate_limit"],
    enableAdapterSwitching: true,
    maxAdapterAttempts: 3,
    memoryBlocks: {
        user: { limit: 5000, filePath: "data/user.txt" }
    },
    compressionTrigger: "lines" as const,
    compressionThreshold: 100,
    compressionPrompt: "Compress this content...",
    enableDebug: true,
    uploadErrorDumps: false
};

/**
 * 中间件使用示例
 */
import { SimpleMiddleware } from "../src/middlewares/SimpleMiddleware";
import { MessageContext } from "../src/middlewares/base";

// 自定义中间件示例
class CustomLoggingMiddleware extends SimpleMiddleware {
    constructor(ctx: Context) {
        super("custom-logging", ctx);
    }

    async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
        this.logger.info(`处理来自用户 ${ctx.koishiSession.userId} 的消息`);
        
        const startTime = Date.now();
        await next();
        const duration = Date.now() - startTime;
        
        this.logger.info(`消息处理完成，耗时 ${duration}ms`);
    }
}

// 使用自定义中间件
function createAgentWithCustomMiddleware(ctx: Context): SimpleAgent {
    const agent = new SimpleAgentBuilder(ctx)
        .withDebug(true)
        .build();
    
    // 注册自定义中间件
    const middlewareManager = agent.getServices().getMiddlewareManager();
    middlewareManager.use(new CustomLoggingMiddleware(ctx));
    
    return agent;
}

export {
    createAgentWithBuilder,
    createAgentWithPreset,
    createAgentWithAgentBuilder,
    createAgentForEnvironment,
    createAgentWithMergedConfig,
    createAgentWithCustomMiddleware,
    CustomLoggingMiddleware
};
