## 任务系统
相比人类来说，AI调用工具执行任务的速度非常快，比如搜索，但有时我们需要AI”慢“一点，以模仿人类执行任务的速度。

另一方面，由于默认提示词比较简单，在聊天交互模式下，AI难以有效整合多个工具来完成复杂任务。

因此，可以设计一个任务管理系统，来辅助AI完成任务。

提供三个方法：

- submit_task：提交一个任务，返回一个任务ID。
- get_task_status：根据任务ID获取任务状态。
- get_task_result：根据任务ID获取任务结果。

任务状态包括：

- pending：任务正在等待执行。
- running：任务正在执行。
- completed：任务已完成。
- failed：任务执行失败。

任务结果包括：

- result：任务执行结果。
- error：任务执行错误信息。

任务会在后台执行，根据传入参数不同，分为立即唤醒和主动获取结果两种模式。

如果是立即唤醒模式，在任务完成后，会立即触发AI回复流程

如果是主动获取结果模式，在下一次AI回复时，会检查任务状态，如果任务完成，会将任务结果返回给AI。

同时在提示词中说明当前任务列表和任务状态


对于更复杂的任务，可以通过多Agent协作来完成。

或者为常见任务设计任务链，来指导AI完成任务。


### 与日程管理系统的集成
可以将AI每天的日程解析为任务，通过设定任务开始时间和结束时间来模拟Bot日程。
同时这些任务还可以是具体的操作，比如早上定时发送消息。（有点像cron）

一个完整的调用流程如下：

1. 创建一个任务，指定预计完成时间，任务描述和需要调用的工具。
2. 构造提示词指导Agent如何完成任务。
3. 调用Agent完成任务。
4. 检查任务状态，如果任务完成，将任务结果返回给AI。

在等待任务完成的过程中，AI可以根据任务状态和预计完成时间来调整自己的回复策略和内容。
