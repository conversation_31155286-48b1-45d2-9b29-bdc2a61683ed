import Database from "libsql";

const path = process.env.DATABASE ?? ":memory:";

const db = new Database(path);

interface User {
    id: string
    name: string
    email: string
}

db.exec("CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT, email TEXT)");
db.exec(
  "INSERT INTO users (id, name, email) VALUES (1, 'Alice', '<EMAIL>')"
);

const row = db.prepare("SELECT * FROM users WHERE id = ?").get(1) as User;

console.log(`Name: ${row.name}, email: ${row.email}`);