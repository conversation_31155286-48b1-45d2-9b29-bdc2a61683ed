# Yes! I'm Bo<PERSON>! 核心开发文档

## 1. 项目概述

`Yes! I'm Bot!` 是一个基于 Koishi 框架的智能机器人插件，旨在为机器人赋予“灵魂”，使其能够进行更自然、智能的对话和交互。它通过集成大型语言模型 (LLM)、记忆管理、工具调用和世界状态感知等核心能力，实现了一个高度可配置和可扩展的 AI 代理。

### 核心目标

*   **智能对话**: 利用 LLM 进行自然语言理解和生成，实现流畅的对话体验。
*   **记忆能力**: 维护长期和短期记忆，使机器人能够记住上下文和用户偏好。
*   **工具调用**: 允许机器人调用外部工具来执行特定任务，如发送消息、查询信息等。
*   **世界状态感知**: 跟踪频道、成员和对话回合的状态，为 AI 提供丰富的上下文信息。
*   **高度可配置**: 提供灵活的配置选项，允许用户根据需求调整机器人行为。
*   **可扩展性**: 设计为模块化架构，方便开发者添加新的模型、工具和中间件。

## 2. 核心架构

`packages/core/src` 目录是 `Yes! I'm Bot!` 插件的核心代码库，其架构围绕 `AgentCore` 类展开，该类负责协调各个组件以实现主要的消息处理流程。整个系统采用模块化设计，通过 Koishi 的服务和插件机制进行集成。

### 2.1 `AgentCore`

[`packages/core/src/agent.ts`](packages/core/src/agent.ts) 中的 `AgentCore` 是插件的入口点和协调者。它在 Koishi 上下文 (`Context`) 中注册，并在 `ready` 事件触发时进行初始化。

**主要职责**:

*   **服务初始化**: 负责初始化 `ServiceContainer`、`DatabaseManager`、`ServiceInitializer` 和 `MiddlewareConfigurator` 等核心服务。
*   **中间件注册**: 配置并注册 Koishi 的消息处理中间件，这是所有传入消息的入口。
*   **生命周期管理**: 在插件启动时初始化，并在插件销毁时清理资源。

**初始化流程 (`initialize` 方法)**:

1.  **注册数据库表**: 通过 `DatabaseManager` 注册所有必要的数据库表。
2.  **初始化服务**: 通过 `ServiceInitializer` 初始化并注册各种核心服务（如模型服务、记忆服务、工具管理器等）。
3.  **配置中间件**: 通过 `MiddlewareConfigurator` 构建和配置消息处理中间件链。
4.  **注册消息处理中间件**: 将配置好的中间件链注册到 Koishi 的 `ctx.middleware()` 中，开始处理传入消息。

### 2.2 Koishi 服务集成

`Yes! I'm Bot!` 作为一个 Koishi 插件，充分利用了 Koishi 的服务 (`Service`) 机制。[`packages/core/src/index.ts`](packages/core/src/index.ts) 中的 `YesImBot` 类是插件的根服务，它负责：

*   **本地化**: 定义多语言支持。
*   **插件注册**: 将 `ToolManager`、`ModelService`、`MemoryManager` 和 `DataManager` 等核心功能作为 Koishi 插件注册到上下文中，使其可被其他组件访问。
*   **命令注册**: 在 `ready` 事件后注册各种 Koishi 命令（如 `cache`、`config`、`context`、`extension`）。

## 3. 主要模块/服务

### 3.1 模型服务 (`ModelService`)

*   **文件路径**: [`packages/core/src/adapters/index.ts`](packages/core/src/adapters/index.ts)
*   **职责**: 负责管理和提供不同 LLM 提供商（如 OpenAI, Ollama, Anthropic）的聊天和嵌入模型实例。它采用工厂模式 (`IProviderFactory`) 来创建具体的客户端。
*   **核心类**:
    *   `ModelService`: Koishi 服务，维护提供商工厂注册表和已实例化的提供商缓存。
    *   `IProviderFactory`: 定义了创建 `IProviderClient` 的接口。
    *   `IProviderClient`: 定义了 LLM 提供商客户端的统一结构，包含 `chat`、`embed` 等能力。
    *   `ChatModel`: 封装了具体的聊天模型实例，提供统一的 `chat` 接口，处理流式/非流式请求、自定义参数和重试逻辑。
    *   `EmbedModel`: 封装了嵌入模型实例，提供 `embed` 和 `embedMany` 接口。
    *   `ProviderInstance`: 封装了特定提供商的配置和客户端，负责根据模型 ID 获取 `ChatModel` 或 `EmbedModel`。
    *   `ChatModelSwitcher`: 根据配置的模型优先级，在多个聊天模型之间进行切换，实现故障转移。

### 3.2 记忆管理 (`MemoryManager`)

*   **文件路径**: [`packages/core/src/memories/index.ts`](packages/core/src/memories/index.ts), [`packages/core/src/memories/services/`](packages/core/src/memories/services/), [`packages/core/src/memories/stores/`](packages/core/src/memories/stores/)
*   **职责**: 管理机器人的核心记忆和归档记忆，并提供记忆压缩功能。
*   **核心组件**:
    *   `MemoryManager`: Koishi 服务，协调 `CoreMemoryService`、`CompressionService` 和 `ArchivalMemoryStore`。
    *   `CoreMemoryService`: 管理核心记忆块 (`MemoryBlock`) 的生命周期和操作（追加、替换、清除）。核心记忆通常是机器人长期持有的关键信息（如人设、长期目标）。
    *   `MemoryBlock`: 代表一个独立的记忆块，可以存储多行内容，并支持文件绑定和内容操作。
    *   `CompressionService`: 负责根据配置（行数、字符数、消息间隔、时间间隔）对核心记忆块进行压缩汇总，以减少上下文长度。它使用 LLM 来生成压缩后的摘要。
    *   `IMemoryBlockStore`: 记忆块存储接口，目前有 `DatabaseMemoryBlockStore` 实现，将记忆存储在 Koishi 数据库中。
    *   `IArchivalMemoryStore`: 归档记忆存储接口，目前有 `InMemoryArchivalStore` 实现，用于存储大量非核心但可检索的信息，并支持基于嵌入的语义搜索。

### 3.3 中间件系统 (`Middleware`)

*   **文件路径**: [`packages/core/src/middlewares/base.ts`](packages/core/src/middlewares/base.ts), [`packages/core/src/middlewares/impl/`](packages/core/src/middlewares/impl/)
*   **职责**: 定义了消息处理的管道，每个中间件负责处理消息流中的特定阶段。
*   **核心类**:
    *   `MiddlewareManager`: 管理中间件链的执行，允许按顺序执行注册的中间件。
    *   `Middleware`: 所有具体中间件的基类，定义了 `execute` 方法。
    *   `MessageContext`: 封装了 Koishi `Session` 和其他上下文信息，在中间件链中传递。
    *   `CheckReplyCondition`: 检查是否需要回复消息，根据 `@提及`、`意愿阈值` 和 `对话流分析` 等策略进行判断。
    *   `DatabaseStorageMiddleware`: 将接收到的消息和图片存储到数据库中，并更新世界状态。
    *   `ErrorHandlingMiddleware`: 捕获中间件链中的错误，进行日志记录，并可选择上传错误报告。
    *   `LLMProcessingMiddleware`: 核心的 LLM 调用逻辑，负责构建提示词，并使用 `LLMRetryManager` 和 `LLMAdapterManager` 处理 LLM 请求的重试和适配器切换。
    *   `ResponseHandlingMiddleware`: 解析 LLM 的响应（包括思考过程和工具调用），执行工具，并处理连续对话 (`Heartbeat`) 逻辑。

### 3.4 工具管理 (`ToolManager`)

*   **文件路径**: [`packages/core/src/extensions/index.ts`](packages/core/src/extensions/index.ts), [`packages/core/src/extensions/manager.ts`](packages/core/src/extensions/manager.ts)
*   **职责**: 允许机器人调用外部函数或服务来执行特定任务。
*   **核心类**:
    *   `ToolManager`: Koishi 服务，负责工具的注册、管理和执行。支持从文件加载工具，并提供热重载功能。
    *   `Tool`: 定义了工具的元数据 (`ToolMetadata`) 和执行逻辑 (`execute` 方法)。
    *   `ToolExecutionContext`: 提供了工具执行时的上下文信息，如 Koishi `Context` 和 `Session`。
    *   `createTool`, `createExtension`, `withCommonParams`: 辅助函数，用于简化工具和扩展的创建。

### 3.5 世界状态 (`WorldState`)

*   **文件路径**: [`packages/core/src/services/worldstate/DataManager.ts`](packages/core/src/services/worldstate/DataManager.ts), [`packages/core/src/services/worldstate/interfaces.ts`](packages/core/src/services/worldstate/interfaces.ts), [`packages/core/src/services/worldstate/model.ts`](packages/core/src/services/worldstate/model.ts)
*   **职责**: 维护机器人对当前对话环境和历史的理解，包括频道、成员、对话回合和 Agent 响应。
*   **核心类**:
    *   `DataManager`: Koishi 服务，负责世界状态数据的持久化和检索。它扩展了 Koishi 数据库模型，注册了 `yesimbot_channels`、`yesimbot_members`、`yesimbot_turns`、`yesimbot_agent_responses` 等表。它提供了 `touchChannel`、`touchMember`、`startNewTurn`、`endTurn`、`addMessageEvent`、`addAgentResponse` 等方法来管理这些数据。
    *   `Channel`: 频道信息，包含 ID、平台、类型、最后活跃时间、当前活跃回合 ID 和对话历史。
    *   `Member`: 成员信息，包含 ID、平台、频道 ID、用户 ID、名称、昵称和最后活跃时间。
    *   `Turn`: 对话回合，代表一次完整的用户与 Agent 之间的交互，从用户消息开始到 Agent 回复结束。包含开始/结束时间、状态、事件和 Agent 响应。
    *   `ChannelEvent`: 记录频道内发生的各种事件，如用户消息、工具调用、工具结果等。
    *   `AgentResponse`: Agent 的完整响应，包括思考过程 (`Thoughts`)、执行的动作 (`Action`) 和工具执行结果 (`ActionResult`)。

### 3.6 配置管理 (`Config`)

*   **文件路径**: [`packages/core/src/config/index.ts`](packages/core/src/config/index.ts), [`packages/core/src/config/schemas/`](packages/core/src/config/schemas/)
*   **职责**: 定义了插件的所有可配置项及其 Schema 验证规则。
*   **核心结构**:
    *   `Config` 接口: 定义了插件的整体配置结构，包括 `ModelServiceConfig`、`Chat`、`LLM`、`ReplyCondition`、`Memory`、`ImageViewer`、`ToolManagerConfig`、`ToolCall`、`PromptTemplate` 和 `Debug` 等模块的配置。
    *   `Schema.object`: Koishi 的 Schema 定义，用于对配置项进行类型检查、默认值设置、描述和 UI 渲染提示。
    *   `schemas` 目录: 包含各个模块的独立 Schema 定义，如 `adapters.schema.ts`、`chat.schema.ts`、`memory.schema.ts`、`middleware.schema.ts`。

### 3.7 提示词构建 (`PromptBuilder`)

*   **文件路径**: [`packages/core/src/prompts/PromptBuilder.ts`](packages/core/src/prompts/PromptBuilder.ts)
*   **职责**: 动态构建发送给 LLM 的系统提示词和用户提示词。它利用 Mustache 模板引擎和数据提供者来填充模板。
*   **核心类**:
    *   `PromptBuilder`: 负责注册局部模板 (`Partial`) 和数据提供者 (`PromptDataProvider`)。它能够根据模板中定义的占位符，异步获取所需数据（如核心记忆、工具定义、世界状态）并渲染最终提示词。
    *   `PromptDataProvider`: 类型定义，表示一个异步函数，用于获取特定提示词部分所需的数据。
    *   `SystemBaseTemplate`, `UserBaseTemplate`: 默认的系统和用户提示词模板，从 `resources/prompts` 目录加载。

### 3.8 错误处理 (`ErrorHandlingMiddleware`)

*   **文件路径**: [`packages/core/src/middlewares/impl/ErrorHandling.ts`](packages/core/src/middlewares/impl/ErrorHandling.ts), [`packages/core/src/shared/errors/`](packages/core/src/shared/errors/)
*   **职责**: 捕获并处理插件运行过程中发生的错误，提供详细的日志记录和可选的错误报告上传功能。
*   **核心类**:
    *   `ErrorHandlingMiddleware`: 作为中间件链的一部分，它使用 `try-catch` 块来捕获后续中间件抛出的异常。
    *   `BaseError`: 所有自定义错误的基类，提供了统一的错误结构和日志格式化方法。
    *   `LLMRequestError`, `LLMTimeoutError`, `LLMRetryExhaustedError`, `LLMAdapterError`: LLM 请求相关的特定错误类型，用于区分不同原因的 LLM 失败。
    *   `MemoryError`: 记忆服务相关的错误。
    *   错误报告: 能够收集系统信息、会话上下文、LLM 响应等，并格式化为 Markdown 报告，可上传到指定服务。

## 4. 命令

`Yes! I'm Bot!` 插件通过 Koishi 命令系统提供了一系列管理和调试功能。这些命令在 [`packages/core/src/commands/`](packages/core/src/commands/) 目录下定义。

*   **`cache.ts`**:
    *   `cache`: 图片缓存管理。
    *   `cache.clean [days]`: 清理过期图片缓存（默认7天）。
    *   `cache.clear`: 清除所有图片缓存。
*   **`config.ts`**:
    *   `conf.get [key]`: 获取指定配置项的值，支持深层嵌套和数组索引。
    *   `conf.set [key] [value]`: 设置指定配置项的值，支持深层嵌套和数组索引，并尝试智能解析值类型。
*   **`context.ts`**:
    *   `添加消息 <content>`: 在指定场景末尾添加系统消息，可指定频道和发送者。
    *   `清空对话 [-t target] [-p person]`: 清除 BOT 的对话上下文，支持清除当前会话、指定会话、所有群聊或私聊、或指定用户的记忆。
    *   `压缩记忆 <label>`: 压缩指定核心记忆块的内容。
*   **`extension.ts`**:
    *   `扩展列表`: 显示已安装的扩展列表，包括文件名、名称、版本、作者和描述。
    *   `删除扩展 <fileName> [-f]`: 删除指定扩展文件，支持强制删除。
    *   `重载插件`: 重载插件以使扩展变更生效。
    *   `安装扩展 <url> [-f filename]`: 从 URL 安装扩展文件，可指定保存文件名。

## 5. 数据模型

插件在 Koishi 数据库中使用了以下主要表来持久化数据：

*   **`yesimbot_messages`**: 存储聊天消息历史。
    *   `messageId`: 消息唯一 ID (Primary Key)
    *   `sender`: 发送者信息 (ID, name, nick)
    *   `channel`: 频道信息 (ID, type)
    *   `timestamp`: 消息时间
    *   `content`: 消息内容
*   **`yesimbot_memory_blocks`**: 存储核心记忆块的内容。
    *   `label`: 记忆块标签 (Primary Key)
    *   `content`: 记忆块内容 (字符串数组)
    *   `timestamp`: 最后更新时间
*   **`yesimbot_interactions`**: 存储工具交互记录。
    *   `id`: 交互唯一 ID (Primary Key)
    *   `emitter`: 触发者
    *   `emitter_channel_id`: 触发频道
    *   `type`: 交互类型 (`tool_call`, `tool_result`, `message_sent`)
    *   `functionName`: 工具名称
    *   `toolParams`: 工具参数 (JSON)
    *   `toolResult`: 工具结果 (Object)
    *   `life`: 生命周期计数器
    *   `timestamp`: 交互时间
*   **`yesimbot_last_reply`**: 存储频道最后回复时间。
    *   `channelId`: 频道 ID (Primary Key)
    *   `timestamp`: 最后回复时间
*   **`yesimbot_images`**: 存储图片数据。
    *   `id`: 图片唯一 ID (Primary Key)
    *   `mimeType`: MIME 类型
    *   `base64`: Base64 编码数据
    *   `summary`: 简要总结
    *   `desc`: 详细描述
    *   `size`: 大小 (字节)
    *   `timestamp`: 存储时间
*   **`yesimbot_channels`**: 存储频道世界状态信息。
    *   `id`: 频道 ID (Primary Key)
    *   `platform`: 平台
    *   `type`: 类型 (`group`, `private`)
    *   `lastActive`: 最后活跃时间
    *   `activeTurnId`: 当前活跃回合 ID
*   **`yesimbot_members`**: 存储成员世界状态信息。
    *   `channelId`, `userId`: 组合 Primary Key
    *   `platform`: 平台
    *   `name`, `nick`: 名称和昵称
    *   `lastActive`: 最后活跃时间
*   **`yesimbot_turns`**: 存储对话回合信息。
    *   `id`: 回合唯一 ID (Primary Key)
    *   `platform`: 平台
    *   `channelId`: 频道 ID
    *   `startTime`, `endTime`: 开始和结束时间
    *   `status`: 状态 (`active`, `completed`, `aborted`)
*   **`yesimbot_agent_responses`**: 存储 Agent 的响应。
    *   `id`: 响应唯一 ID (Primary Key)
    *   `turnId`: 所属回合 ID
    *   `timestamp`: 响应时间
    *   `thoughts`: 思考过程 (JSON)
    *   `actions`: 执行的动作 (JSON)
    *   `observations`: 工具执行结果 (JSON)

## 6. 扩展性

`Yes! I'm Bot!` 被设计为高度可扩展，主要通过以下方式：

*   **自定义 LLM 提供商**: 通过实现 `IProviderFactory` 和 `IProviderClient` 接口，可以轻松集成新的 LLM 服务。
*   **自定义工具**: 开发者可以创建新的工具，通过 `ToolManager` 注册，扩展机器人的能力。工具可以是简单的函数，也可以是复杂的外部服务调用。
*   **自定义中间件**: 通过实现 `Middleware` 接口，可以在消息处理管道的任何阶段插入自定义逻辑，例如添加新的回复策略、数据处理或日志记录。
*   **自定义记忆存储**: 通过实现 `IMemoryBlockStore` 和 `IArchivalMemoryStore` 接口，可以替换或扩展记忆的持久化方式（例如，使用向量数据库作为归档记忆）。
*   **自定义提示词模板**: 通过修改配置文件中的 `PromptTemplate`，可以调整 LLM 的行为和输出格式。

## 7. 开发指南

### 7.1 环境搭建

1.  **安装 Koishi**: 确保你已经安装了 Koishi CLI 和相关的依赖。
2.  **克隆项目**: 将 `Yes! I'm Bot!` 项目克隆到本地。
3.  **安装依赖**: 在项目根目录和 `packages/core` 目录下运行 `npm install`。

### 7.2 运行与调试

1.  **配置插件**: 根据你的需求修改 `koishi.config.ts` 或通过 Koishi 控制台配置 `Yes! I'm Bot!` 插件。
2.  **启动 Koishi**: 运行 `koishi start` 启动机器人。
3.  **调试**: 使用 VS Code 等 IDE 附加到 Node.js 进程进行调试。

### 7.3 贡献

欢迎社区贡献！如果你有任何功能建议、Bug 报告或代码改进，请通过 GitHub Pull Request 或 Issue 进行提交。

---

**注意**: 本文档将随着项目的发展而持续更新。