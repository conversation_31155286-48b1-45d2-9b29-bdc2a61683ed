<world_state timestamp="${updateTimestamp}">
    <active_channels>
        <channel id="group_chat_123" type="group">
            <name>AI爱好者茶话会</name>
            <description>一个讨论最新AI技术的地方。</description>
            <!-- 经过智能筛选和摘要的成员信息 -->
            <members>
                <!-- 层次1: 核心成员 -->
                <user id="letta_bot_id" role="you">Letta</user>
                <user id="owner_id" role="owner">Neo</user>

                <!-- 层次2: 上下文相关成员 (近期发言或被@) -->
                <user id="user_trinity">Trinity</user>
                <user id="user_morpheus">Morpheus</user>
            </members>

            <!-- 层次3: 群体氛围感知的摘要信息 -->
            <member_summary>
                <total_count>1000</total_count>
                <online_count>150</online_count>
                <active_roles>
                    <role name="游戏开发者" count="25"/>
                    <role name="科幻作家" count="10"/>
                    <role name="普通成员" count="965"/>
                </active_roles>
                <recent_active_members_count>35</recent_active_members_count> <!-- 近24小时发言人数 -->
            </member_summary>

            <!-- 新的、基于Turn的对话历史 -->
            <history>
                <!-- 一个完整的、过去的Turn -->
                <turn id="turn_001" status="full">
                    <events>

                        <!-- 事件1: 新成员入群 -->
                        <event type="user_joined" timestamp="2025-05-07T10:30:00Z">
                            <actor id="system">System</actor>
                            <user id="user_david">David</user>
                            <note>David was invited by Alice.</note> <!-- 可选的附注信息 -->
                        </event>

                        <!-- 事件2: 成员离开 -->
                        <event type="user_left" timestamp="2025-05-07T10:30:15Z">
                            <actor id="user_bob">Bob</actor> <!-- 执行操作的人 -->
                            <user id="user_charlie">Charlie</user> <!-- 被操作的人 -->
                            <reason>Kicked by admin.</reason>
                        </event>

                        <!-- 事件3: 用户发送消息 -->
                        <event type="message_batch" start_timestamp="2025-05-07T11:00:15Z" end_timestamp="2025-05-07T11:01:00Z">
                            [#msg1 2025-05-07T09:55:00Z Alice<user_A>] Letta, 你知道最新的AI模型是什么吗？
                        </event>

                        <!-- 事件4: 系统通知 (私聊中) -->
                        <event type="system_notification" timestamp="2025-05-07T10:31:00Z">
                            <actor id="system">System</actor>
                            <content>Your memory usage is approaching 90%.</content>
                        </event>

                        <!-- 事件5: 定时心跳 (没有其他事件时) -->
                        <event type="heartbeat_tick" timestamp="2025-05-07T10:35:00Z">
                            <actor id="system">System</actor>
                            <note>No new user activity in the last 5 minutes.</note>
                        </event>

                    </events>
                    <responses>
                        <response>
                            <thoughts>
                                <observe>Alice询问了最新的AI模型。</observe>
                                <analyze_infer>这是一个知识性问题，需要实时信息。</analyze_infer>
                                <plan>我将使用web_search来查找信息，并需要一次心跳来处理结果。</plan>
                            </thoughts>
                            <actions>
                                <action function="web_search" query="latest large language model"/>
                            </actions>
                            <observations>
                                <observation function="web_search" success="true" result="最新的模型是..."/>
                            </observations>
                        </response>
                        <!-- 如果有心跳，这里会有第二个response -->
                        <response>
                            <thoughts>...</thoughts>
                            <actions>
                                <action function="send_message" message="我查到啦！最新的模型是..."/>
                            </actions>
                            <observations>
                                <observation function="send_message" success="true"/>
                            </observations>
                        </response>
                    </responses>
                </turn>

                <!-- 一个新的、需要处理的Turn -->
                <turn id="turn_004" status="new">
                    <events>

                        <!-- 关键事件1: 保持完整结构 -->
                        <event type="user_joined" timestamp="2025-05-07T11:00:00Z">
                            <actor id="system">System</actor>
                            <user id="user_eva">Eva</user>
                        </event>

                        <!-- 消息批处理事件: 压缩连续的消息 -->
                        <event type="message_batch" start_timestamp="2025-05-07T11:00:15Z" end_timestamp="2025-05-07T11:01:00Z">
                            <!-- 内部使用您之前设计的高度压缩的格式 -->
                            [#msg5 2025-05-07T11:00:15Z Eva<user_eva>] 大家好！我是Eva！
                            [#msg6 2025-05-07T11:00:45Z Alice<user_A>] @Eva 欢迎欢迎！
                            [#msg7 2025-05-07T11:01:00Z Bob<user_B>] 新人爆照！(玩笑)
                        </event>

                        <!-- 关键事件2: 再次出现，打断了消息流，保持完整结构 -->
                        <event type="system_notification" timestamp="2025-05-07T11:02:00Z">
                            <actor id="system">System</actor>
                            <content>Reminder: The 'meme_generator' tool is currently offline for maintenance.</content>
                        </event>

                    </events>
                </turn>
            </history>
        </channel>
        <!-- 其他频道... -->
    </active_channels>
</world_state>